const { test, expect } = require('@playwright/test');

test.describe('API Documentation Code Block Formatting', () => {
    test('Code blocks have no unnecessary empty lines at beginning and end', async ({ page }) => {
        console.log('API documentation code block formatting test start');
        
        // Navigate to admin login
        await page.goto('http://localhost:8000/admin/');
        
        // Login as admin
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');
        await page.click('button[type="submit"]');
        
        // Navigate to API documentation
        await page.goto('http://localhost:8000/admin/api.php');
        
        // Wait for page to load
        await page.waitForSelector('.code-block', { timeout: 10000 });
        
        // Get all code blocks
        const codeBlocks = page.locator('.code-block');
        const count = await codeBlocks.count();
        
        console.log(`Found ${count} code blocks to check`);
        
        // Check each code block for unnecessary empty lines
        for (let i = 0; i < count; i++) {
            const codeBlock = codeBlocks.nth(i);
            const text = await codeBlock.textContent();
            const lines = text.split('\n');
            
            // Check that first line is not empty (or only whitespace)
            const firstLineEmpty = lines[0].trim() === '';
            
            // Check that last line is not empty (or only whitespace)  
            const lastLineEmpty = lines[lines.length - 1].trim() === '';
            
            console.log(`Code block ${i}: First line empty: ${firstLineEmpty}, Last line empty: ${lastLineEmpty}`);
            
            // Verify no unnecessary empty lines at start and end
            expect(firstLineEmpty).toBe(false);
            expect(lastLineEmpty).toBe(false);
        }
        
        // Verify specific formatting for different code block types
        const authBlock = page.locator('.code-block').first();
        const authText = await authBlock.textContent();
        expect(authText.trim()).toMatch(/^Authorization: Bearer/);
        
        const errorBlock = page.locator('.error-example');
        const errorText = await errorBlock.textContent();
        expect(errorText.trim()).toMatch(/^\/\/ 401 Unauthorized/);
        
        const responseBlock = page.locator('.response-example');
        const responseText = await responseBlock.textContent();
        expect(responseText.trim()).toMatch(/^\{/);
        
        console.log('API documentation code block formatting test end - Success: All code blocks properly formatted without unnecessary empty lines');
    });
});
