const { test, expect } = require('@playwright/test');
const { TestCleanup } = require('./utils/cleanup');

test.describe('Remove Direct Post.php - SEO URLs Only', () => {
    let cleanup;

    test.beforeAll(async () => {
        cleanup = new TestCleanup();
    });

    test.afterAll(async ({ browser }) => {
        console.log('remove_post_php_cleanup start');
        
        if (cleanup) {
            const page = await browser.newPage();
            
            // Login to admin
            await page.goto('http://localhost:8000/admin/');
            await page.fill('input[name="username"]', 'admin');
            await page.fill('input[name="password"]', 'admin');
            await page.click('button[type="submit"]');
            await page.waitForURL('**/admin/index.php');
            
            // Clean up all tracked posts
            await cleanup.cleanupPosts(page);
            
            // Also clean up any remaining test posts
            await cleanup.cleanupTestPosts(page);
            
            await page.close();
        }
        
        console.log('remove_post_php_cleanup end');
    });
    
    test.beforeEach(async ({ page }) => {
        console.log('remove_post_php_test start');
        // Go to admin login
        await page.goto('http://localhost:8000/admin/');
        
        // Login as admin
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');
        await page.click('button[type="submit"]');
        
        // Wait for redirect to admin dashboard
        await page.waitForURL('**/admin/index.php');
        console.log('Admin login successful');
    });
    
    test.afterEach(async ({ page }) => {
        console.log('remove_post_php_test end');
        await page.close();
    });

    test('should handle post.php access with redirect or 404', async ({ page }) => {
        console.log('test_redirect_old_urls start');
        
        // Test that post.php access either redirects properly or returns 404
        // For invalid IDs, should return 404
        const response404 = await page.goto('http://localhost:8000/post.php?id=99999', { 
            waitUntil: 'domcontentloaded' 
        });
        expect(response404.status()).toBe(404);
        
        // For no ID, should return 404  
        const responseNoId = await page.goto('http://localhost:8000/post.php', { 
            waitUntil: 'domcontentloaded' 
        });
        expect(responseNoId.status()).toBe(404);
        
        console.log('Post.php URL handling verified');
        console.log('test_redirect_old_urls end');
    });

    test('should return 404 for direct post.php access', async ({ page }) => {
        console.log('test_direct_access_404 start');
        
        // Try to access post.php directly (should fail after we remove it)
        const response = await page.goto('http://localhost:8000/post.php', { 
            waitUntil: 'domcontentloaded' 
        });
        
        // Should return 404 after we remove the file
        expect(response.status()).toBe(404);
        
        console.log('Direct post.php access correctly returns 404');
        console.log('test_direct_access_404 end');
    });

    test('should ensure all admin post links use SEO URLs', async ({ page }) => {
        console.log('test_admin_seo_links start');
        
        // Go to posts admin page
        await page.goto('http://localhost:8000/admin/posts.php');
        
        // Get all post links on the page
        const postLinks = await page.locator('a[href*="/"]').filter({ 
            hasText: /^(?!.*admin).*$/ // Exclude admin links
        }).evaluateAll(links => 
            links.map(link => link.href).filter(href => 
                !href.includes('admin') && 
                !href.includes('post.php') &&
                (href.includes('/tech/') || href.includes('/lifestyle/') || href.includes('/travel/'))
            )
        );
        
        // Verify all links are SEO-friendly format
        for (const link of postLinks) {
            expect(link).toMatch(/\/(tech|lifestyle|travel)\/[a-z0-9\-]+\/$/);
        }
        
        console.log(`Verified ${postLinks.length} post links use SEO URLs`);
        console.log('test_admin_seo_links end');
    });
});
