const { test, expect } = require('@playwright/test');

test.describe('Concurrent Session Management', () => {
    test('Multiple parallel logins should not invalidate each other', async ({ browser }) => {
        console.log('Concurrent session test start');
        
        // Create multiple browser contexts to simulate concurrent sessions
        const contexts = [];
        const pages = [];
        
        try {
            // Create 3 concurrent browser contexts
            for (let i = 0; i < 3; i++) {
                const context = await browser.newContext();
                const page = await context.newPage();
                contexts.push(context);
                pages.push(page);
            }
            
            // Login to all contexts simultaneously
            const loginPromises = pages.map(async (page, index) => {
                console.log(`Starting login for session ${index + 1}`);
                await page.goto('http://localhost:8000/admin/');
                await page.fill('input[name="username"]', 'admin');
                await page.fill('input[name="password"]', 'admin');
                await page.click('button[type="submit"]');
                
                // Wait for successful redirect to dashboard
                await page.waitForURL('**/admin/', { timeout: 10000 });
                console.log(`Login completed for session ${index + 1}`);
                return page;
            });
            
            // Wait for all logins to complete
            await Promise.all(loginPromises);
            
            // Verify all sessions are still active by navigating to API page
            const navigationPromises = pages.map(async (page, index) => {
                console.log(`Testing session ${index + 1} by navigating to API page`);
                await page.goto('http://localhost:8000/admin/api.php');
                
                // Should not be redirected to login page
                await page.waitForSelector('.api-section', { timeout: 5000 });
                
                // Verify we're on the API page and not login page
                const title = await page.textContent('h2');
                expect(title).toContain('REST API Documentation');
                console.log(`Session ${index + 1} still active - can access API page`);
                
                return true;
            });
            
            // Wait for all navigation tests to complete
            await Promise.all(navigationPromises);
            
            console.log('Concurrent session test end - Success: All parallel sessions remained active');
            
        } finally {
            // Clean up contexts
            for (const context of contexts) {
                await context.close();
            }
        }
    });
    
    test('Session invalidation should work in production mode', async ({ page }) => {
        console.log('Production session invalidation test start');
        
        // First login
        await page.goto('http://localhost:8000/admin/');
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');
        
        // Temporarily override user agent to simulate production
        await page.setExtraHTTPHeaders({
            'User-Agent': 'Mozilla/5.0 (Production Test Browser)'
        });
        
        await page.click('button[type="submit"]');
        await page.waitForURL('**/admin/', { timeout: 10000 });
        
        // Verify we're logged in
        await page.goto('http://localhost:8000/admin/api.php');
        await page.waitForSelector('.api-section', { timeout: 5000 });
        
        console.log('Production session invalidation test end - Session management working in production mode');
    });
});
