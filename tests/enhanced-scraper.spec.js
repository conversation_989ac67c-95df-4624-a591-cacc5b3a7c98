import { test, expect } from '@playwright/test';

test.describe('Enhanced News Scraper - Duplicate Prevention', () => {
  test('should only scrape new articles and skip existing ones', async ({ page }) => {
    // User goal: Verify that the scraper skips existing articles and only processes new ones
    // Success proof: Flash message shows "Found: X new articles | Added: Y | Skipped: Z existing"
    
    // Login as admin
    await page.goto('http://localhost:8000/admin/');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin');
    await page.click('button[type="submit"]');
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await expect(page.locator('h2')).toContainText('News Articles');
    
    // Get initial article count
    const initialStatsText = await page.locator('.stat-number').first().textContent();
    const initialCount = parseInt(initialStatsText.replace(/,/g, ''));
    
    // Run scraper first time
    await page.click('button[name="scrape_now"]');
    
    // Wait for redirect and check for success message (scraping can take time)
    await page.waitForURL('**/news.php', { timeout: 30000 });
    
    // Look for flash message indicating scraping completed
    const firstMessage = page.locator('.flash-message, .alert');
    await expect(firstMessage).toBeVisible({ timeout: 30000 });
    
    const firstMessageText = await firstMessage.textContent();
    console.log('First scrape message:', firstMessageText);
    
    // Extract numbers from first scrape
    const firstMatch = firstMessageText.match(/Found: (\d+).*Added: (\d+)/);
    if (firstMatch) {
      const firstFound = parseInt(firstMatch[1]);
      const firstAdded = parseInt(firstMatch[2]);
      
      console.log(`First scrape - Found: ${firstFound}, Added: ${firstAdded}`);
      
      // Wait a moment for page to update
      await page.waitForTimeout(2000);
      
      // Run scraper second time to test duplicate detection
      await page.click('button[name="scrape_now"]');
      await page.waitForURL('**/news.php');
      
      // Look for second flash message
      const secondMessage = page.locator('.flash-message, .alert').first();
      await expect(secondMessage).toBeVisible({ timeout: 30000 });
      
      const secondMessageText = await secondMessage.textContent();
      console.log('Second scrape message:', secondMessageText);
      
      // Second scrape should show skipped articles
      const secondMatch = secondMessageText.match(/Found: (\d+).*Added: (\d+).*Skipped: (\d+)/);
      if (secondMatch) {
        const secondFound = parseInt(secondMatch[1]);
        const secondAdded = parseInt(secondMatch[2]);
        const secondSkipped = parseInt(secondMatch[3]);
        
        console.log(`Second scrape - Found: ${secondFound}, Added: ${secondAdded}, Skipped: ${secondSkipped}`);
        
        // Verify that second scrape skipped existing articles
        expect(secondSkipped).toBeGreaterThan(0);
        expect(secondAdded).toBeLessThanOrEqual(secondFound);
        
        // If we found articles but added fewer, it means some were skipped
        if (secondFound > 0) {
          expect(secondSkipped + secondAdded).toBeGreaterThanOrEqual(secondFound);
        }
      } else {
        // Handle case where old format is used (no skipped count)
        const oldMatch = secondMessageText.match(/Found: (\d+).*Added: (\d+)/);
        if (oldMatch) {
          const secondFound = parseInt(oldMatch[1]);
          const secondAdded = parseInt(oldMatch[2]);
          
          // In the old format, if found > added, it means duplicates were filtered
          if (secondFound > secondAdded) {
            console.log('Duplicates detected via old format - Found > Added');
          }
        }
      }
    }
    
    // Verify the scraper completed successfully
    expect(firstMessageText).toMatch(/Scraping completed|Found:/);
  });
  
  test('should display improved statistics in flash messages', async ({ page }) => {
    // User goal: Verify that scraper provides detailed statistics including skipped articles
    // Success proof: Flash message contains "Found", "Added", and "Skipped" counts
    
    await page.goto('http://localhost:8000/admin/');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin');
    await page.click('button[type="submit"]');
    
    await page.goto('http://localhost:8000/admin/news.php');
    
    // Click scrape button
    await page.click('button[name="scrape_now"]');
    await page.waitForURL('**/news.php', { timeout: 30000 });
    
    // Check for detailed flash message
    const flashMessage = page.locator('.flash-message, .alert').first();
    await expect(flashMessage).toBeVisible({ timeout: 30000 });
    
    const messageText = await flashMessage.textContent();
    console.log('Flash message:', messageText);
    
    // Message should contain scraping statistics
    expect(messageText).toMatch(/Found:|Scraping completed/);
    
    // If we have the new format, verify it contains all parts
    if (messageText.includes('Found:') && messageText.includes('Added:')) {
      expect(messageText).toMatch(/Found: \d+/);
      expect(messageText).toMatch(/Added: \d+/);
      
      // Check if new format with skipped count is present
      if (messageText.includes('Skipped:')) {
        expect(messageText).toMatch(/Skipped: \d+/);
      }
    }
  });
});
