const { test, expect } = require('@playwright/test');

test.describe('Featured Articles', () => {
    test.beforeEach(async ({ page }) => {
        // Go to login page and authenticate
        await page.goto('http://localhost:8000/admin/');
        
        // Check if already logged in
        const currentUrl = page.url();
        if (currentUrl.includes('/admin/login.php')) {
            await page.fill('#username', 'admin');
            await page.fill('#password', 'admin123');
            await page.click('button[type="submit"]');
            await page.waitForURL('**/admin/**');
        }
    });

    test('should show featured checkbox in post edit form', async ({ page }) => {
        // Go to posts page
        await page.goto('http://localhost:8000/admin/posts.php');
        
        // Click on "Create New Post" button
        await page.click('a[href="posts.php?action=new"]');
        
        // Wait for the form to load
        await page.waitForSelector('form.post-form');
        
        // Check that the featured checkbox is present
        const featuredCheckbox = page.locator('input[name="is_featured"]');
        await expect(featuredCheckbox).toBeVisible();
        
        // Check the label text
        const featuredLabel = page.locator('text=Mark as featured article');
        await expect(featuredLabel).toBeVisible();
        
        // Check the help text
        const helpText = page.locator('text=Maximum of 2 articles can be featured');
        await expect(helpText).toBeVisible();
    });

    test('should allow creating a featured article', async ({ page }) => {
        // Go to create new post page
        await page.goto('http://localhost:8000/admin/posts.php?action=new');
        
        // Fill out the form
        await page.fill('#title', 'Test Featured Article ' + Date.now());
        await page.fill('#subtitle', 'This is a test featured article');
        await page.fill('#description', 'This is a test description for the featured article');
        await page.fill('#content', 'This is the content of the test featured article.');
        await page.fill('#thumbnail_url', 'https://via.placeholder.com/600x400');
        
        // Check the featured checkbox
        await page.check('input[name="is_featured"]');
        
        // Uncheck draft to publish immediately
        await page.uncheck('input[name="is_draft"]');
        
        // Submit the form
        await page.click('button[type="submit"]');
        
        // Wait for redirect to edit page or posts list
        await page.waitForURL('**/admin/posts.php**');
        
        // Check for success message
        const successMessage = page.locator('.flash-message.success');
        await expect(successMessage).toBeVisible();
    });

    test('should prevent featuring more than 2 articles', async ({ page }) => {
        // First, let's create 2 featured articles
        for (let i = 1; i <= 2; i++) {
            await page.goto('http://localhost:8000/admin/posts.php?action=new');
            
            await page.fill('#title', `Featured Article ${i} - ${Date.now()}`);
            await page.fill('#subtitle', `Subtitle for featured article ${i}`);
            await page.fill('#description', `Description for featured article ${i}`);
            await page.fill('#content', `Content for featured article ${i}`);
            await page.fill('#thumbnail_url', 'https://via.placeholder.com/600x400');
            
            await page.check('input[name="is_featured"]');
            await page.uncheck('input[name="is_draft"]');
            
            await page.click('button[type="submit"]');
            await page.waitForURL('**/admin/posts.php**');
            
            // Verify success
            const successMessage = page.locator('.flash-message.success');
            await expect(successMessage).toBeVisible();
        }
        
        // Now try to create a third featured article
        await page.goto('http://localhost:8000/admin/posts.php?action=new');
        
        await page.fill('#title', `Third Featured Article - ${Date.now()}`);
        await page.fill('#subtitle', 'This should fail');
        await page.fill('#description', 'This third featured article should not be allowed');
        await page.fill('#content', 'This should fail because we already have 2 featured articles');
        await page.fill('#thumbnail_url', 'https://via.placeholder.com/600x400');
        
        await page.check('input[name="is_featured"]');
        await page.uncheck('input[name="is_draft"]');
        
        await page.click('button[type="submit"]');
        
        // Should see an error message
        await page.waitForSelector('.flash-message.error');
        const errorMessage = page.locator('.flash-message.error');
        await expect(errorMessage).toBeVisible();
        await expect(errorMessage).toContainText('Maximum of 2 featured articles');
    });

    test('should allow editing featured status of existing posts', async ({ page }) => {
        // Go to posts list
        await page.goto('http://localhost:8000/admin/posts.php');
        
        // Find the first post and click edit
        const firstEditLink = page.locator('a[href*="action=edit"]').first();
        await firstEditLink.click();
        
        // Wait for edit form to load
        await page.waitForSelector('form.post-form');
        
        // Check the featured checkbox (if not already checked)
        const featuredCheckbox = page.locator('input[name="is_featured"]');
        const isChecked = await featuredCheckbox.isChecked();
        
        if (!isChecked) {
            await featuredCheckbox.check();
        } else {
            await featuredCheckbox.uncheck();
        }
        
        // Submit the form
        await page.click('button[type="submit"]');
        
        // Wait for redirect and verify success
        await page.waitForURL('**/admin/posts.php**');
        const successMessage = page.locator('.flash-message.success');
        await expect(successMessage).toBeVisible();
    });

    test('should show featured status in posts list', async ({ page }) => {
        // First create a featured article to ensure we have one
        await page.goto('http://localhost:8000/admin/posts.php?action=new');
        
        const testTitle = 'Featured List Test - ' + Date.now();
        await page.fill('#title', testTitle);
        await page.fill('#subtitle', 'Test subtitle');
        await page.fill('#description', 'Test description');
        await page.fill('#content', 'Test content');
        await page.fill('#thumbnail_url', 'https://via.placeholder.com/600x400');
        
        await page.check('input[name="is_featured"]');
        await page.uncheck('input[name="is_draft"]');
        
        await page.click('button[type="submit"]');
        await page.waitForURL('**/admin/posts.php**');
        
        // Go to posts list
        await page.goto('http://localhost:8000/admin/posts.php');
        
        // The posts list should exist
        await page.waitForSelector('.posts-table');
        
        // We should see our created post in the list
        const postRow = page.locator(`text=${testTitle}`);
        await expect(postRow).toBeVisible();
    });
});
