// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('Scraper Progress Bar', () => {
  test('should show progress bar when scraping starts and complete successfully', async ({ page }) => {
    // User goal: Verify that the progress bar appears and updates when the "Scrape Now" button is clicked
    // Success criteria: Progress bar is visible, percentage increases, and completion message is shown
    
    console.log('Starting scraper progress bar test...');
    
    // Navigate to admin news page
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForTimeout(2000);
    
    // Login if needed
    const loginForm = page.locator('form');
    if (await loginForm.isVisible()) {
      console.log('Logging in as admin...');
      await page.fill('input[name="username"]', 'admin');
      await page.fill('input[name="password"]', 'admin');
      await page.click('button[type="submit"]');
      await page.waitForTimeout(2000);
      
      // Navigate to news page after login
      await page.goto('http://localhost:8000/admin/news.php');
      await page.waitForTimeout(2000);
    }
    
    // Find the scrape button
    const scrapeButton = page.locator('#scrapeButton');
    await expect(scrapeButton).toBeVisible();
    await expect(scrapeButton).toHaveText('🔄 Scrape Now');
    
    // Check that progress container is initially hidden
    const progressContainer = page.locator('#progressContainer');
    await expect(progressContainer).toBeHidden();
    
    console.log('Clicking scrape button...');
    
    // Click the scrape button
    await scrapeButton.click();
    
    // Wait for progress container to appear
    await expect(progressContainer).toBeVisible({ timeout: 5000 });
    console.log('✅ Progress container is now visible');
    
    // Check that button is disabled and text changed
    await expect(scrapeButton).toBeDisabled();
    await expect(scrapeButton).toHaveText('⏳ Scraping...');
    console.log('✅ Scrape button is disabled and shows progress text');
    
    // Check progress bar elements are present
    const progressBar = page.locator('#progressBar');
    const progressPercentage = page.locator('#progressPercentage');
    const progressMessage = page.locator('#progressMessage');
    const statusIcon = page.locator('#statusIcon');
    
    await expect(progressBar).toBeVisible();
    await expect(progressPercentage).toBeVisible();
    await expect(progressMessage).toBeVisible();
    await expect(statusIcon).toBeVisible();
    console.log('✅ All progress elements are visible');
    
    // Wait for progress to start
    await page.waitForTimeout(3000);
    
    // Check that progress percentage is displayed
    const percentageText = await progressPercentage.textContent();
    expect(percentageText).toMatch(/\d+%/);
    console.log(`✅ Progress percentage shown: ${percentageText}`);
    
    // Check that progress message is displayed
    const messageText = await progressMessage.textContent();
    expect(messageText).toBeTruthy();
    if (messageText) {
      expect(messageText.length).toBeGreaterThan(0);
    }
    console.log(`✅ Progress message shown: ${messageText}`);
    
    // Wait for completion (up to 60 seconds)
    console.log('Waiting for scraping to complete...');
    
    let completed = false;
    let attempts = 0;
    const maxAttempts = 30; // 30 attempts * 2 seconds = 60 seconds max
    
    while (!completed && attempts < maxAttempts) {
      await page.waitForTimeout(2000);
      attempts++;
      
      // Check if completion occurred
      const currentPercentage = await progressPercentage.textContent();
      const currentMessage = await progressMessage.textContent();
      
      console.log(`Attempt ${attempts}: ${currentPercentage} - ${currentMessage}`);
      
      if (currentPercentage === '100%' || (currentMessage && currentMessage.includes('Completed'))) {
        completed = true;
        console.log('✅ Scraping completed!');
        break;
      }
    }
    
    // Verify completion state
    if (completed) {
      // Check for completion indicators
      const finalMessage = await progressMessage.textContent();
      expect(finalMessage).toContain('Completed');
      console.log(`✅ Final completion message: ${finalMessage}`);
      
      // Check if status icon shows completion
      const hasCompletedClass = await statusIcon.evaluate(el => el.classList.contains('completed'));
      expect(hasCompletedClass).toBe(true);
      console.log('✅ Status icon shows completion state');
      
      // Wait for page reload (should happen automatically)
      await page.waitForTimeout(4000);
      
      console.log('✅ Scraper progress bar test completed successfully');
    } else {
      console.log('⚠️ Test completed but scraping may still be in progress');
    }
  });
  
  test('should handle scraper progress updates correctly', async ({ page }) => {
    // User goal: Verify that progress updates are received and displayed correctly
    // Success criteria: Progress bar updates with different percentages and messages
    
    console.log('Starting progress updates test...');
    
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForTimeout(2000);
    
    // Login if needed
    const loginForm = page.locator('form');
    if (await loginForm.isVisible()) {
      await page.fill('input[name="username"]', 'admin');
      await page.fill('input[name="password"]', 'admin');
      await page.click('button[type="submit"]');
      await page.waitForTimeout(2000);
      await page.goto('http://localhost:8000/admin/news.php');
      await page.waitForTimeout(2000);
    }
    
    // Verify the progress endpoint exists
    const response = await page.request.get('/admin/scraper-progress.php?action=progress');
    expect(response.status()).toBe(200);
    console.log('✅ Progress endpoint is accessible');
    
    // Verify the start endpoint exists and requires authentication
    const startResponse = await page.request.post('/admin/scraper-progress.php?action=start');
    expect(startResponse.status()).toBe(200);
    console.log('✅ Start endpoint is accessible');
    
    console.log('✅ Progress updates test completed successfully');
  });
});
