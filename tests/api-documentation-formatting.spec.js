const { test, expect } = require('@playwright/test');

test.describe('API Documentation Formatting', () => {
    test('API documentation displays properly formatted code examples', async ({ page }) => {
        console.log('API documentation formatting test start');
        
        // Navigate to login page first
        await page.goto('http://localhost:8000/admin/');
        
        // Login as admin
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');
        await page.click('button[type="submit"]');
        
        // Navigate to API documentation
        await page.goto('http://localhost:8000/admin/api.php');
        
        // Wait for page to load
        await page.waitForSelector('.api-section', { timeout: 10000 });
        
        // Check that error responses are properly formatted (should have line breaks)
        const errorResponsesBlock = page.locator('.error-example');
        const errorText = await errorResponsesBlock.textContent();
        
        // Verify line breaks exist in error responses
        expect(errorText).toContain('401 Unauthorized');
        expect(errorText).toContain('400 Bad Request');
        expect(errorText).toContain('500 Internal Server Error');
        
        // Check Python example formatting
        const codeBlocks = page.locator('.code-block');
        const pythonBlock = codeBlocks.last();
        const pythonText = await pythonBlock.textContent();
        
        // Verify Python code has proper structure
        expect(pythonText).toContain('import requests');
        expect(pythonText).toContain('url =');
        expect(pythonText).toContain('headers = {');
        expect(pythonText).toContain('data = {');
        expect(pythonText).toContain('response = requests.post');
        
        // Verify that code blocks preserve whitespace
        const codeBlockStyles = await page.locator('.code-block').first().evaluate(el => 
            window.getComputedStyle(el).whiteSpace
        );
        expect(codeBlockStyles).toBe('pre');
        
        console.log('API documentation formatting test end - Success: Code examples display with proper formatting');
    });
});
