const { test, expect } = require('@playwright/test');

test.describe('Concurrent Login Fix Verification', () => {
    test('Multiple browsers can login simultaneously without invalidating each other', async ({ browser }) => {
        console.log('Concurrent login fix verification start');
        
        // Create 2 browser contexts to test concurrent sessions
        const context1 = await browser.newContext();
        const context2 = await browser.newContext();
        
        const page1 = await context1.newPage();
        const page2 = await context2.newPage();
        
        try {
            // Login both sessions at the same time
            const login1 = async () => {
                console.log('Browser 1: Starting login');
                await page1.goto('http://localhost:8000/admin/');
                await page1.fill('input[name="username"]', 'admin');
                await page1.fill('input[name="password"]', 'admin');
                await page1.click('button[type="submit"]');
                await page1.waitForURL('**/admin/');
                console.log('Browser 1: Login successful');
            };
            
            const login2 = async () => {
                console.log('Browser 2: Starting login');
                await page2.goto('http://localhost:8000/admin/');
                await page2.fill('input[name="username"]', 'admin');
                await page2.fill('input[name="password"]', 'admin');
                await page2.click('button[type="submit"]');
                await page2.waitForURL('**/admin/');
                console.log('Browser 2: Login successful');
            };
            
            // Run both logins concurrently
            await Promise.all([login1(), login2()]);
            
            // Verify both sessions remain active by accessing a protected page
            await page1.goto('http://localhost:8000/admin/api.php');
            await page2.goto('http://localhost:8000/admin/api.php');
            
            // Both should see the API documentation page, not be redirected to login
            const title1 = await page1.textContent('h2');
            const title2 = await page2.textContent('h2');
            
            expect(title1).toContain('REST API Documentation');
            expect(title2).toContain('REST API Documentation');
            
            console.log('Concurrent login fix verification end - Success: Both sessions remain active');
            
        } finally {
            await context1.close();
            await context2.close();
        }
    });
});
