import { test, expect } from '@playwright/test';

test('scraping shows found and added article counts', async ({ page }) => {
  console.log('Testing scraping result display with article counts');
  
  // Navigate to admin login
  await page.goto('http://localhost:8000/admin/');
  
  // Login as admin
  await page.fill('input[name="username"]', 'admin');
  await page.fill('input[name="password"]', 'admin');
  await page.click('button[type="submit"]');
  
  // Navigate to news page
  await page.goto('http://localhost:8000/admin/news.php');
  await page.waitForTimeout(2000);
  
  // Click scrape now button
  const scrapeButton = page.locator('button[name="scrape_now"]');
  await expect(scrapeButton).toBeVisible();
  
  console.log('Starting scraping process...');
  await scrapeButton.click();
  
  // Wait for the page to reload and show the result
  await page.waitForTimeout(15000); // Give enough time for scraping
  
  // Look for the success message with article counts
  const successMessage = page.locator('.alert-success');
  await expect(successMessage).toBeVisible({ timeout: 5000 });
  
  const messageText = await successMessage.textContent();
  console.log('Success message:', messageText);
  
  // Verify the message contains the expected format
  expect(messageText).toMatch(/✅ Scraping completed! Found: \d+ articles \| Added: \d+ new articles/);
  
  console.log('Test passed: Article counts are displayed in the success message');
});
