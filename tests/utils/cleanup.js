/**
 * Test Cleanup Utilities
 * Handles cleanup of test data to prevent database pollution
 */

class TestCleanup {
    constructor() {
        this.createdPosts = [];
        this.testTitles = [];
    }

    /**
     * Track a post created during testing
     */
    trackPost(postId, title) {
        console.log(`cleanup_track_post start: ID ${postId}, Title: ${title}`);
        
        if (postId) {
            this.createdPosts.push(postId);
        }
        if (title) {
            this.testTitles.push(title);
        }
        
        console.log(`cleanup_track_post end: Tracking ${this.createdPosts.length} posts`);
    }

    /**
     * Track a post by title pattern (for cases where ID is not available)
     */
    trackPostByTitle(titlePattern) {
        console.log(`cleanup_track_title start: ${titlePattern}`);
        this.testTitles.push(titlePattern);
        console.log(`cleanup_track_title end`);
    }

    /**
     * Clean up all tracked posts
     */
    async cleanupPosts(page) {
        console.log(`cleanup_posts start: ${this.createdPosts.length} IDs, ${this.testTitles.length} titles`);
        
        if (this.createdPosts.length === 0 && this.testTitles.length === 0) {
            console.log('cleanup_posts end: No posts to clean');
            return;
        }

        try {
            // Navigate to admin posts page
            await page.goto('http://localhost:8000/admin/posts.php', { waitUntil: 'domcontentloaded' });
            
            // Delete by title patterns (more reliable for test posts)
            for (const titlePattern of this.testTitles) {
                await this.deletePostByTitle(page, titlePattern);
            }

            // Clear the tracking arrays
            this.createdPosts = [];
            this.testTitles = [];
            
            console.log('cleanup_posts end: Cleanup completed successfully');
            
        } catch (error) {
            console.error('Cleanup error:', error.message);
            console.log('cleanup_posts end: Cleanup failed');
        }
    }

    /**
     * Delete a post by title pattern
     */
    async deletePostByTitle(page, titlePattern) {
        console.log(`delete_post_by_title start: ${titlePattern}`);
        
        try {
            // Find posts matching the title pattern
            const postRows = await page.locator('tr').filter({ 
                hasText: titlePattern 
            }).all();
            
            for (const row of postRows) {
                // Look for delete button in this row
                const deleteButton = row.locator('button[onclick*="deletePost"]');
                
                if (await deleteButton.count() > 0) {
                    // Click delete button
                    await deleteButton.click();
                    
                    // Wait for confirmation dialog and confirm
                    page.once('dialog', async dialog => {
                        console.log(`Confirming deletion of post: ${titlePattern}`);
                        await dialog.accept();
                    });
                    
                    // Wait a moment for the deletion to process
                    await page.waitForTimeout(500);
                    
                    console.log(`Deleted post: ${titlePattern}`);
                }
            }
            
        } catch (error) {
            console.log(`Could not delete post ${titlePattern}: ${error.message}`);
        }
        
        console.log(`delete_post_by_title end: ${titlePattern}`);
    }

    /**
     * Clean up posts by searching for test patterns
     */
    async cleanupTestPosts(page) {
        console.log('cleanup_test_posts start');
        
        try {
            await page.goto('http://localhost:8000/admin/posts.php', { waitUntil: 'domcontentloaded' });
            
            // Common test post patterns
            const testPatterns = [
                'Test Post',
                'SEO Test',
                'Subtitle Test',
                'URL Redirect',
                'Test Creation'
            ];
            
            for (const pattern of testPatterns) {
                await this.deletePostsByPattern(page, pattern);
            }
            
            console.log('cleanup_test_posts end: Test posts cleanup completed');
            
        } catch (error) {
            console.error('Test cleanup error:', error.message);
            console.log('cleanup_test_posts end: Test cleanup failed');
        }
    }

    /**
     * Delete posts matching a pattern
     */
    async deletePostsByPattern(page, pattern) {
        console.log(`delete_posts_by_pattern start: ${pattern}`);
        
        try {
            // Look for any posts containing the pattern
            const posts = await page.locator('tr').filter({ 
                hasText: new RegExp(pattern, 'i') 
            }).all();
            
            console.log(`Found ${posts.length} posts matching pattern: ${pattern}`);
            
            for (const post of posts) {
                const deleteButton = post.locator('button[onclick*="deletePost"]');
                
                if (await deleteButton.count() > 0) {
                    await deleteButton.click();
                    
                    // Handle confirmation dialog
                    page.once('dialog', async dialog => await dialog.accept());
                    
                    await page.waitForTimeout(300);
                }
            }
            
        } catch (error) {
            console.log(`Error cleaning pattern ${pattern}: ${error.message}`);
        }
        
        console.log(`delete_posts_by_pattern end: ${pattern}`);
    }
}

module.exports = { TestCleanup };
