const { test, expect } = require('@playwright/test');

test.describe('Fixed Scraper Progress', () => {
  test('should track progress from start to completion with incremental updates', async ({ page }) => {
    console.log('Test goal: Verify scraper progress tracking shows incremental updates and final completion message');
    
    await page.goto('http://localhost:8000/admin/');
    
    // Login
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    
    // Wait for the scrape button
    await page.waitForSelector('.btn-primary', { timeout: 5000 });
    
    // Record start time
    const startTime = Date.now();
    
    // Click scrape button
    await page.click('.btn-primary');
    
    // Wait for progress container to appear quickly
    await page.waitForSelector('#progress-container', { visible: true, timeout: 3000 });
    
    const responseTime = Date.now() - startTime;
    console.log(`Progress started in ${responseTime}ms`);
    
    // Verify quick response (should be under 2 seconds)
    expect(responseTime).toBeLessThan(2000);
    
    // Track progress updates
    const progressUpdates = [];
    let isCompleted = false;
    let pollCount = 0;
    const maxPolls = 40; // 20 seconds max
    
    while (!isCompleted && pollCount < maxPolls) {
      await page.waitForTimeout(500);
      pollCount++;
      
      const progressText = await page.textContent('#progress-text');
      const progressBar = await page.locator('#progress-bar');
      const progressValue = await progressBar.getAttribute('aria-valuenow');
      const percentage = parseInt(progressValue || '0');
      
      if (progressText && percentage !== null) {
        // Only log new progress
        const lastUpdate = progressUpdates[progressUpdates.length - 1];
        if (!lastUpdate || lastUpdate.percentage !== percentage || lastUpdate.text !== progressText) {
          console.log(`Progress: ${percentage}% - ${progressText}`);
          progressUpdates.push({ percentage, text: progressText });
        }
        
        // Check for completion
        if (percentage === 100 && progressText.includes('Completed!')) {
          isCompleted = true;
          console.log(`✓ Completed: ${progressText}`);
        }
      }
    }
    
    // Verify we got meaningful progress updates
    expect(progressUpdates.length).toBeGreaterThan(2);
    console.log(`Total progress updates: ${progressUpdates.length}`);
    
    // Verify completion was reached
    expect(isCompleted).toBe(true);
    
    // Verify final message format
    const finalUpdate = progressUpdates[progressUpdates.length - 1];
    expect(finalUpdate.text).toMatch(/✅ Completed!/);
    expect(finalUpdate.text).toMatch(/Found: \\d+/);
    expect(finalUpdate.text).toMatch(/Added: \\d+/);
    expect(finalUpdate.text).toMatch(/Skipped: \\d+/);
    expect(finalUpdate.percentage).toBe(100);
    
    console.log('✓ Progress tracking verified - quick start and proper completion');
  });
});
