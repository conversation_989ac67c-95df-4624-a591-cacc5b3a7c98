const { test } = require('@playwright/test');
const { TestCleanup } = require('./utils/cleanup');

/**
 * Global test cleanup
 * Runs after all other tests to clean up any remaining test data
 */
test.describe.serial('Global Test Cleanup', () => {
    
    test('should clean up all test posts created during test runs', async ({ page }) => {
        console.log('global_cleanup start');
        
        const cleanup = new TestCleanup();
        
        try {
            // Login to admin
            await page.goto('http://localhost:8000/admin/');
            await page.fill('input[name="username"]', 'admin');
            await page.fill('input[name="password"]', 'admin');
            await page.click('button[type="submit"]');
            await page.waitForURL('**/admin/index.php');
            console.log('Admin login successful for cleanup');
            
            // Clean up all test posts by common patterns
            await cleanup.cleanupTestPosts(page);
            
            console.log('Global cleanup completed successfully');
            
        } catch (error) {
            console.error('Global cleanup failed:', error.message);
            // Don't fail the test, just log the error
        }
        
        console.log('global_cleanup end');
    });
});
