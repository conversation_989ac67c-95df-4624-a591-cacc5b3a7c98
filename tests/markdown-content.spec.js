const { test, expect } = require('@playwright/test');

test.describe('Markdown Content Extraction', () => {
  test('should extract articles in markdown format with clean formatting', async ({ page }) => {
    // User goal: Verify that scraper extracts article content in markdown format with proper formatting
    // Success proof: Articles have markdown-formatted content without consecutive empty lines
    
    // Login as admin
    await page.goto('http://localhost:8000/admin/');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await expect(page.locator('h2')).toContainText('News Articles');
    
    // Check if articles already exist, otherwise run scraper
    const articleCards = page.locator('.article-card');
    let articleCount = await articleCards.count();
    
    if (articleCount === 0) {
      // Only run scraper if no articles exist
      await page.click('button[name="scrape_now"]');
      await page.waitForURL('**/news.php', { timeout: 45000 });
      
      // Wait for flash message indicating scraping completed
      const flashMessage = page.locator('.flash-messages .flash-success, .flash-messages .flash-info').first();
      await expect(flashMessage).toBeVisible({ timeout: 30000 });
      
      // Recount articles after scraping
      articleCount = await articleCards.count();
    }
    
    if (articleCount > 0) {
      // Expand content of first article to check markdown formatting
      const firstExpandButton = page.locator('.btn-expand').first();
      if (await firstExpandButton.isVisible()) {
        await firstExpandButton.click();
        
        // Check for markdown elements in expanded content
        const expandedContent = page.locator('.content-full').first();
        await expect(expandedContent).toBeVisible();
        
        const contentText = await expandedContent.textContent();
        console.log('Content sample:', contentText.substring(0, 200));
        
        // Verify content doesn't have multiple consecutive empty lines
        expect(contentText).not.toMatch(/\n\s*\n\s*\n/); // No triple line breaks
        
        // Content should be present and formatted
        expect(contentText.length).toBeGreaterThan(50);
        
        // Check for markdown links if present
        if (contentText.includes('[') && contentText.includes('](')) {
          console.log('Markdown links detected in content');
        }
        
        console.log('Markdown content extraction test completed successfully');
      } else {
        console.log('No expandable content found, checking for basic content');
        const contentPreview = page.locator('.content-preview').first();
        if (await contentPreview.isVisible()) {
          const previewText = await contentPreview.textContent();
          expect(previewText.length).toBeGreaterThan(20);
          console.log('Basic content verified');
        }
      }
    } else {
      console.log('No articles found to test markdown formatting');
    }
  });

  test('should handle content cleaning correctly', async ({ page }) => {
    // User goal: Verify that content cleaning removes consecutive empty lines properly
    // Success proof: Articles don't contain multiple consecutive empty lines in their content
    
    // Login and navigate to news
    await page.goto('http://localhost:8000/admin/');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    await page.goto('http://localhost:8000/admin/news.php');
    
    // Check existing articles for proper formatting
    const articleCards = page.locator('.article-card');
    const articleCount = await articleCards.count();
    
    if (articleCount > 0) {
      // Check multiple articles for content quality
      const articlesToCheck = Math.min(3, articleCount);
      
      for (let i = 0; i < articlesToCheck; i++) {
        const expandButton = articleCards.nth(i).locator('.btn-expand');
        
        if (await expandButton.isVisible()) {
          await expandButton.click();
          await page.waitForTimeout(500);
          
          const fullContent = articleCards.nth(i).locator('.content-full');
          const contentText = await fullContent.textContent();
          
          // Verify no consecutive empty lines (no triple newlines)
          expect(contentText).not.toMatch(/\n\s*\n\s*\n/);
          
          // Collapse back
          const collapseButton = articleCards.nth(i).locator('.btn-collapse');
          await collapseButton.click();
          await page.waitForTimeout(200);
        }
      }
      
      console.log(`Content cleaning verified for ${articlesToCheck} articles`);
    } else {
      console.log('No articles available to test content cleaning');
    }
  });
});
