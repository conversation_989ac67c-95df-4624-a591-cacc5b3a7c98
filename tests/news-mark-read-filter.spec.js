import { test, expect } from '@playwright/test';

test('mark as read should preserve current filter', async ({ page }) => {
  console.log('Testing mark as read filter preservation');
  
  // Navigate to admin login
  await page.goto('http://localhost:8000/admin/');
  
  // Login as admin
  await page.fill('input[name="username"]', 'admin');
  await page.fill('input[name="password"]', 'admin');
  await page.click('button[type="submit"]');
  
  // Navigate to news unread filter
  await page.goto('http://localhost:8000/admin/news.php?filter=unread');
  await page.waitForTimeout(2000);
  
  // Verify we're on unread filter
  const unreadFilterLink = page.locator('a.filter-link.active').first();
  await expect(unreadFilterLink).toContainText('Unread');
  
  // Find the first "Mark as Read" button
  const markReadButton = page.locator('button[name="mark_read"]').first();
  
  if (await markReadButton.count() > 0) {
    console.log('Found Mark as Read button, clicking it');
    await markReadButton.click();
    
    // Wait for page reload
    await page.waitForTimeout(2000);
    
    // Check if we're still on unread filter
    const currentUrl = page.url();
    console.log('Current URL after mark as read:', currentUrl);
    
    // The URL should still contain filter=unread
    expect(currentUrl).toContain('filter=unread');
    
    // The active filter should still be "Unread"
    const activeFilter = page.locator('a.filter-link.active').first();
    await expect(activeFilter).toContainText('Unread');
    
    console.log('Test passed: Filter preserved after marking as read');
  } else {
    console.log('No unread articles available for testing');
    // This is acceptable - we just need to verify the fix works when articles exist
  }
});
