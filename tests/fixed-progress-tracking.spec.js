const { test, expect } = require('@playwright/test');

test.describe('Fixed Progress Tracking', () => {
  test('should show incremental progress and final completion message', async ({ page }) => {
    console.log('Test goal: Verify progress tracking shows steps and completion message correctly');
    
    // Navigate to admin login
    await page.goto('http://localhost:8000/admin/');
    
    // Login
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin');
    await page.click('button[type="submit"]');
    
    // Wait for any successful page load after login
    await page.waitForTimeout(2000);
    
    // Go directly to news page
    await page.goto('http://localhost:8000/admin/news.php');
    
    // Wait for page to load completely
    await page.waitForSelector('.btn-primary:has-text("🔄 Scrape Now")', { timeout: 5000 });
    
    // Clear previous logs
    await page.evaluate(() => {
      return fetch('/admin/test-clear-logs.php', { method: 'POST' });
    });
    
    // Start scraping and track progress
    await page.click('.btn-primary:has-text("🔄 Scrape Now")');
    
    // Wait for progress bar to appear
    await page.waitForSelector('#progress-container', { visible: true, timeout: 5000 });
    
    // Track progress messages and percentages
    const progressUpdates = [];
    let finalMessage = '';
    let maxPercentage = 0;
    
    // Poll for progress updates
    let isCompleted = false;
    let pollCount = 0;
    const maxPolls = 60; // 30 seconds max
    
    while (!isCompleted && pollCount < maxPolls) {
      await page.waitForTimeout(500);
      pollCount++;
      
      const progressText = await page.textContent('#progress-text');
      const progressBar = await page.locator('#progress-bar');
      const progressValue = await progressBar.getAttribute('aria-valuenow');
      const percentage = parseInt(progressValue || '0');
      
      if (progressText && percentage !== null) {
        console.log(`Progress: ${percentage}% - ${progressText}`);
        
        // Track the progress
        if (percentage > maxPercentage) {
          maxPercentage = percentage;
          progressUpdates.push({ percentage, text: progressText });
        }
        
        // Check if completed
        if (percentage === 100 && progressText.includes('Completed!')) {
          finalMessage = progressText;
          isCompleted = true;
          console.log(`Final message: ${finalMessage}`);
        }
      }
    }
    
    // Verify we got meaningful progress
    expect(progressUpdates.length).toBeGreaterThan(2);
    console.log(`Total progress updates: ${progressUpdates.length}`);
    
    // Verify final completion
    expect(isCompleted).toBe(true);
    expect(finalMessage).toMatch(/✅ Completed!/);
    expect(finalMessage).toMatch(/Found: \\d+/);
    expect(finalMessage).toMatch(/Added: \\d+/);
    expect(finalMessage).toMatch(/Skipped: \\d+/);
    
    // Verify progress increased gradually
    expect(maxPercentage).toBe(100);
    
    // Verify some expected progress steps
    const progressTexts = progressUpdates.map(u => u.text);
    const hasInitializing = progressTexts.some(text => text.includes('Initializing') || text.includes('Starting'));
    const hasProcessing = progressTexts.some(text => text.includes('Processing') || text.includes('Scraping'));
    
    expect(hasInitializing).toBe(true);
    expect(hasProcessing).toBe(true);
    
    console.log('✓ Progress tracking verified successfully');
    console.log(`✓ Final completion message: ${finalMessage}`);
  });
});
