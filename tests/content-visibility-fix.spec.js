import { test, expect } from '@playwright/test';

test.describe('Article Content Visibility Fix', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await page.goto('http://localhost:8000/admin/');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
  });

  test('should display article content after database query fix', async ({ page }) => {
    console.log('content_visibility_fix_test start');
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForLoadState('networkidle');
    
    // Check if articles exist
    const hasArticles = await page.locator('.articles-grid').count() > 0;
    const hasEmptyState = await page.locator('.empty-state').count() > 0;
    
    if (hasEmptyState || !hasArticles) {
      console.log('No articles found, skipping content test');
      console.log('content_visibility_fix_test end');
      return;
    }
    
    // Wait for articles to load
    await page.waitForSelector('.article-card', { timeout: 5000 });
    
    // Check if any article has content preview
    const contentPreviews = await page.locator('.article-content-preview').count();
    console.log(`Found ${contentPreviews} articles with content preview`);
    
    if (contentPreviews > 0) {
      // Content is visible - test passed
      const firstContentPreview = page.locator('.content-preview').first();
      await expect(firstContentPreview).toBeVisible();
      
      // Check if expand button exists for articles with long content
      const expandButtons = await page.locator('.btn-expand').count();
      console.log(`Found ${expandButtons} expand buttons`);
      
      if (expandButtons > 0) {
        // Test expand functionality
        const firstExpandButton = page.locator('.btn-expand').first();
        await firstExpandButton.click();
        
        // Check if full content becomes visible
        const fullContent = page.locator('.content-full').first();
        await expect(fullContent).toBeVisible();
        
        console.log('✅ Content display and expand functionality working!');
      }
    } else {
      console.log('No content previews found - articles may not have content');
    }
    
    console.log('content_visibility_fix_test end');
  });
});
