import { test, expect } from '@playwright/test';

test('news sources edit functionality', async ({ page }) => {
  console.log('Starting news sources edit test');
  
  // Navigate to admin login
  await page.goto('http://localhost:8000/admin/');
  
  // Login as admin
  await page.fill('input[name="username"]', 'admin');
  await page.fill('input[name="password"]', 'admin');
  await page.click('button[type="submit"]');
  
  // Navigate to news sources
  await page.goto('http://localhost:8000/admin/news.php?action=sources');
  await page.waitForTimeout(2000);
  
  // Find the first edit button and click it
  const editButton = page.locator('button.btn-edit').first();
  await expect(editButton).toBeVisible();
  await editButton.click();
  
  // Wait for modal to appear
  await page.waitForSelector('#editSourceModal', { state: 'visible' });
  
  // Check that the selectors are populated
  const articlesSelector = await page.locator('#edit_articles_selector').inputValue();
  const titleSelector = await page.locator('#edit_title_selector').inputValue();
  const thumbnailSelector = await page.locator('#edit_thumbnail_selector').inputValue();
  
  console.log('Articles selector:', articlesSelector);
  console.log('Title selector:', titleSelector);
  console.log('Thumbnail selector:', thumbnailSelector);
  
  // Verify that selectors are not empty
  expect(articlesSelector).not.toBe('');
  expect(titleSelector).not.toBe('');
  expect(thumbnailSelector).not.toBe('');
  
  // Verify specific expected values for first source (Anthropic)
  expect(articlesSelector).toContain('PostCard_post-card');
  expect(titleSelector).toContain('PostCard_post-heading');
  expect(thumbnailSelector).toContain('PostCard_post-card-photo');
  
  console.log('Test passed: selectors are properly populated in edit modal');
});
