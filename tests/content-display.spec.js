import { test, expect } from '@playwright/test';

test.describe('News Content Display', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await page.goto('http://localhost:8000/admin/');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
  });

  test('should display article content with expand/collapse functionality', async ({ page }) => {
    console.log('content_display_test start');
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForLoadState('networkidle');
    
    // Check if articles exist, if not scrape first
    const hasArticles = await page.locator('.articles-grid').count() > 0;
    const hasEmptyState = await page.locator('.empty-state').count() > 0;
    
    if (hasEmptyState || !hasArticles) {
      // No articles, scrape first
      await page.click('button[name="scrape_now"]');
      await page.waitForSelector('.alert', { timeout: 60000 });
      await page.waitForLoadState('networkidle');
    }
    
    // Now check for articles
    await page.waitForSelector('.articles-grid', { timeout: 10000 });
    
    // Check if articles are loaded
    const articleCards = await page.locator('.article-card').count();
    expect(articleCards).toBeGreaterThan(0);
    
    // Look for article with content preview
    const contentPreview = page.locator('.content-preview').first();
    if (await contentPreview.count() > 0) {
      // Check if content preview exists
      await expect(contentPreview).toBeVisible();
      
      // Check if expand button exists
      const expandButton = page.locator('.btn-expand').first();
      if (await expandButton.count() > 0) {
        await expect(expandButton).toBeVisible();
        
        // Click expand button
        await expandButton.click();
        
        // Check if full content is now visible
        const fullContent = page.locator('.content-full').first();
        await expect(fullContent).toBeVisible();
        
        // Check if collapse button exists
        const collapseButton = page.locator('.btn-collapse').first();
        await expect(collapseButton).toBeVisible();
        
        // Click collapse button
        await collapseButton.click();
        
        // Check if full content is hidden again
        await expect(fullContent).not.toBeVisible();
      }
    }
    
    console.log('content_display_test end');
  });

  test('should scrape articles with content and display results', async ({ page }) => {
    console.log('scrape_with_content_test start');
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForLoadState('networkidle');
    
    // Click scrape button
    await page.click('button[name="scrape_now"]');
    
    // Wait for scraping to complete (look for success message)
    await page.waitForSelector('.alert.alert-success', { timeout: 60000 });
    
    // Check that articles found/added summary is displayed
    const successMessage = await page.locator('.alert.alert-success').textContent();
    expect(successMessage).toMatch(/found=\d+.*added=\d+/);
    
    // Check if articles with content are displayed
    await page.waitForSelector('.articles-grid', { timeout: 5000 });
    const articlesWithContent = await page.locator('.article-content-preview').count();
    
    // Should have at least some articles with content
    console.log(`Found ${articlesWithContent} articles with content preview`);
    
    console.log('scrape_with_content_test end');
  });
});
