const { test, expect } = require('@playwright/test');

test.describe('Scraper Progress Start Fix', () => {
  test('action=start should return immediately, not block', async ({ page }) => {
    console.log('Test goal: Verify that scraper start API returns immediately without blocking');
    
    // Navigate to admin and login
    await page.goto('http://localhost:8000/admin/');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin');
    await page.click('button[type="submit"]');
    await page.waitForTimeout(2000);
    
    // Go to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForSelector('.btn-primary:has-text("🔄 Scrape Now")', { timeout: 5000 });
    
    // Clear previous logs
    await page.evaluate(() => {
      return fetch('/admin/clear-logs-test.php', { method: 'POST' });
    });
    
    // Test that the start request returns quickly
    const startTime = Date.now();
    
    // Click scrape button
    await page.click('.btn-primary:has-text("🔄 Scrape Now")');
    
    // Wait for progress bar to appear (this should happen quickly)
    await page.waitForSelector('#progress-container', { visible: true, timeout: 3000 });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`Start request completed in ${duration}ms`);
    
    // Verify the request returned quickly (should be under 2 seconds)
    expect(duration).toBeLessThan(2000);
    
    // Verify progress bar is visible and shows initial progress
    const progressText = await page.textContent('#progress-text');
    const progressBar = await page.locator('#progress-bar');
    const progressValue = await progressBar.getAttribute('aria-valuenow');
    
    console.log(`Initial progress: ${progressValue}% - ${progressText}`);
    
    // Should show some initial progress
    expect(parseInt(progressValue || '0')).toBeGreaterThanOrEqual(0);
    expect(progressText).toBeTruthy();
    
    console.log('✓ Start request returns immediately');
    console.log('✓ Progress tracking begins without blocking');
  });
});
