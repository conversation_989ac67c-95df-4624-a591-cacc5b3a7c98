const { test, expect } = require('@playwright/test');

test.describe('Dynamic XML Sitemap', () => {
    
    test('should generate valid XML sitemap with all content and modern styling', async ({ page }) => {
        console.log('sitemap_test start');
        
        /**
         * User goal: Generate a complete XML sitemap that includes homepage, 
         * category pages, and all published articles for search engine indexing
         * with modern visual styling when viewed in browsers.
         * Success element: Modern styled sitemap page with "🗺️ XML Sitemap" heading and interactive sections.
         */
        
        // Navigate to sitemap
        await page.goto('http://localhost:8000/sitemap.php');
        
        // Verify the page displays the modern styled version
        await expect(page.locator('h1')).toContainText('🗺️ XML Sitemap');
        
        // Verify statistics section is present
        await expect(page.locator('.stat-label').filter({ hasText: 'Total URLs' }).first()).toBeVisible();
        await expect(page.locator('.stat-label').filter({ hasText: 'High Priority' }).first()).toBeVisible();
        await expect(page.locator('.stat-label').filter({ hasText: 'Daily Updates' }).first()).toBeVisible();
        
        // Verify sections are present
        await expect(page.getByRole('heading', { name: '🏠 Homepage' })).toBeVisible();
        await expect(page.getByRole('heading', { name: '📂 Categories' })).toBeVisible();
        await expect(page.getByRole('heading', { name: '📄 Posts' })).toBeVisible();
        
        // Click on Categories section to expand it
        await page.locator('.section-header').filter({ hasText: '📂 Categories' }).click();
        
        // Verify actual URL links are present
        await expect(page.locator('a[href="http://localhost:8000/"]')).toBeVisible();
        await expect(page.locator('a[href*="category=tech"]')).toBeVisible();
        await expect(page.locator('a[href*="category=gaming"]')).toBeVisible();
        
        // Verify change frequency badges are styled correctly
        const changefreqTexts = await page.locator('.changefreq').allTextContents();
        expect(changefreqTexts.some(text => text.includes('daily'))).toBeTruthy();
        expect(changefreqTexts.some(text => text.includes('weekly'))).toBeTruthy();
        expect(changefreqTexts.some(text => text.includes('monthly'))).toBeTruthy();
        
        console.log('Valid XML sitemap with modern styling generated successfully');
        console.log('sitemap_test end');
    });

    test('should include proper metadata for different content types', async ({ page }) => {
        console.log('sitemap_metadata_test start');
        
        // Navigate to sitemap
        await page.goto('http://localhost:8000/sitemap.php');
        
        // Verify the statistics show correct counts
        const totalUrls = await page.locator('.stat-number').first().textContent();
        expect(parseInt(totalUrls)).toBeGreaterThan(4); // At least homepage + categories + some posts
        
        // Verify different change frequencies are present (they might be in collapsed sections, so check they exist)
        await expect(page.locator('.freq-daily')).toHaveCount(1);
        await expect(page.locator('.freq-weekly')).toHaveCount(4); // 4 categories 
        await expect(page.locator('.freq-monthly')).toHaveCount(4); // 4 posts
        
        // Verify priority bars are visible and functioning
        await expect(page.locator('.priority-bar').first()).toBeVisible();
        await expect(page.locator('.priority-fill').first()).toBeVisible();
        
        // Verify last modified dates are in proper format
        const lastmodElements = await page.locator('.lastmod').all();
        expect(lastmodElements.length).toBeGreaterThan(0);
        
        // Verify at least one lastmod matches current date format
        const lastmodText = await lastmodElements[0].textContent();
        expect(lastmodText).toMatch(/\d{4}-\d{2}-\d{2}/);
        
        console.log('Sitemap metadata validation successful');
        console.log('sitemap_metadata_test end');
    });

    test('should only include published posts', async ({ page }) => {
        console.log('sitemap_published_only_test start');
        
        // Navigate to sitemap
        await page.goto('http://localhost:8000/sitemap.php');
        
        // Check the total URLs count in the stats
        const totalUrlsText = await page.locator('.stat-number').first().textContent();
        const urlCount = parseInt(totalUrlsText);
        
        // Should include at least: homepage + 4 categories + some posts
        expect(urlCount).toBeGreaterThanOrEqual(5);
        
        // Verify sections are populated with content
        const sectionCounts = await page.locator('.section-count').allTextContents();
        expect(sectionCounts).toContain('1'); // Homepage
        expect(sectionCounts).toContain('4'); // Categories
        
        // Get all post URLs and verify they follow SEO format
        const postLinks = await page.locator('a[href*="/"][href$="/"]').all();
        const postUrls = [];
        
        for (const link of postLinks) {
            const href = await link.getAttribute('href');
            if (href && href.match(/\/[a-z]+\/[a-z0-9\-]+\/$/)) {
                postUrls.push(href);
            }
        }
        
        console.log(`Found ${postUrls.length} post URLs in sitemap`);
        expect(postUrls.length).toBeGreaterThan(0);
        
        console.log('Published posts validation successful');
        console.log('sitemap_published_only_test end');
    });

    test('should serve XSLT stylesheet for modern sitemap presentation', async ({ page }) => {
        console.log('sitemap_xsl_test start');
        
        /**
         * User goal: Verify that the XSLT stylesheet is accessible and provides
         * modern visual styling for the XML sitemap when viewed in browsers.
         * Success element: XSLT file responds with proper content type and contains styling.
         */
        
        // Check that XSLT stylesheet is accessible
        const xslResponse = await page.goto('http://localhost:8000/sitemap.xsl');
        expect(xslResponse.status()).toBe(200);
        
        const xslContent = await xslResponse.text();
        
        // Verify XSLT structure
        expect(xslContent).toContain('<?xml version="1.0" encoding="UTF-8"?>');
        expect(xslContent).toContain('<xsl:stylesheet');
        expect(xslContent).toContain('xmlns:sitemap="http://www.sitemaps.org/schemas/sitemap/0.9"');
        
        // Verify modern styling elements
        expect(xslContent).toContain('font-family:');
        expect(xslContent).toContain('background: linear-gradient');
        expect(xslContent).toContain('border-radius:');
        expect(xslContent).toContain('box-shadow:');
        
        // Verify interactive elements
        expect(xslContent).toContain('toggleSection');
        expect(xslContent).toContain('collapsed');
        
        // Verify responsive design
        expect(xslContent).toContain('@media (max-width: 768px)');
        
        console.log('XSLT stylesheet validation successful');
        console.log('sitemap_xsl_test end');
    });

    test('should serve raw XML for search engines and bots', async ({ request }) => {
        console.log('sitemap_xml_raw_test start');
        
        /**
         * User goal: Verify that search engines and bots can access the raw XML sitemap
         * without XSLT transformation for proper indexing.
         * Success element: Raw XML response with <?xml version and <urlset> elements.
         */
        
        // Request sitemap with bot-like user agent
        const response = await request.get('http://localhost:8000/sitemap.php', {
            headers: {
                'User-Agent': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
                'Accept': 'application/xml, text/xml, */*'
            }
        });
        
        expect(response.status()).toBe(200);
        
        const content = await response.text();
        
        // Verify raw XML structure (this should be XML, not HTML)
        expect(content).toContain('<?xml version="1.0" encoding="UTF-8"?>');
        expect(content).toContain('<?xml-stylesheet type="text/xsl" href="sitemap.xsl"?>');
        expect(content).toContain('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">');
        expect(content).toContain('</urlset>');
        
        // Verify actual XML structure elements
        expect(content).toContain('<loc>http://localhost:8000/</loc>');
        expect(content).toContain('<changefreq>daily</changefreq>');
        expect(content).toContain('<priority>1.0</priority>');
        
        console.log('Raw XML sitemap accessible for search engines');
        console.log('sitemap_xml_raw_test end');
    });
});
