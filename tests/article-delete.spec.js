const { test, expect } = require('@playwright/test');

test.describe('Article Delete Functionality', () => {
  test.beforeEach(async ({ page }) => {
    console.log('delete_test_setup start');
    // Login as admin
    await page.goto('http://localhost:8000/admin/');
    await page.fill('input[name="username"]', 'admin');
    await page.fill('input[name="password"]', 'admin');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    console.log('delete_test_setup end');
  });

  test('should display delete buttons for individual articles', async ({ page }) => {
    console.log('individual_delete_test start');
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForLoadState('networkidle');
    
    // Check if articles exist, if not scrape first
    const hasEmptyState = await page.locator('.empty-state').isVisible().catch(() => false);
    
    if (hasEmptyState) {
      await page.click('button[name="scrape_now"]');
      await page.waitForSelector('.flash-messages', { timeout: 60000 });
      await page.waitForLoadState('networkidle');
    }
    
    // Wait for articles to load
    const articlesExist = await page.locator('.article-card').count() > 0;
    
    if (articlesExist) {
      // Check if delete buttons exist for each article
      const deleteButtons = await page.locator('.btn-delete').count();
      const articles = await page.locator('.article-card').count();
      
      expect(deleteButtons).toBeGreaterThan(0);
      expect(deleteButtons).toBe(articles);
      
      console.log(`Found ${deleteButtons} delete buttons for ${articles} articles`);
    } else {
      console.log('No articles found to test delete buttons');
    }
    
    console.log('individual_delete_test end');
  });

  test('should display bulk delete dropdown with options', async ({ page }) => {
    console.log('bulk_delete_test start');
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForLoadState('networkidle');
    
    // Check for bulk delete dropdown
    await page.waitForSelector('.bulk-actions-dropdown', { timeout: 10000 });
    
    // Click dropdown to open options
    await page.click('.bulk-delete-btn');
    await page.waitForTimeout(200);
    
    // Check for all required options
    await expect(page.locator('button:has-text("Delete All"):not(:has-text("Read")):not(:has-text("Unread")):not(:has-text("Favorites"))')).toBeVisible();
    await expect(page.locator('button:has-text("Delete All Read")')).toBeVisible();
    await expect(page.locator('button:has-text("Delete All Unread")')).toBeVisible();
    await expect(page.locator('button:has-text("Delete All Not Favorites")')).toBeVisible();
    
    console.log('bulk_delete_test end');
  });

  test('should delete individual article when delete button clicked', async ({ page }) => {
    console.log('delete_individual_article_test start');
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForLoadState('networkidle');
    
    // Check if articles exist
    const articlesExist = await page.locator('.article-card').count() > 0;
    
    if (!articlesExist) {
      console.log('No articles found, skipping individual delete test');
      return;
    }
    
    // Count initial articles
    const initialCount = await page.locator('.article-card').count();
    
    // Handle confirmation dialog
    page.on('dialog', async dialog => {
      console.log('Delete confirmation dialog:', dialog.message());
      await dialog.accept();
    });
    
    // Click first delete button
    await page.click('.btn-delete');
    
    // Wait for page reload and check article count decreased
    await page.waitForLoadState('networkidle');
    const finalCount = await page.locator('.article-card').count();
    
    expect(finalCount).toBe(initialCount - 1);
    
    // Check for success message
    const successMessage = page.locator('.flash-messages .flash-success');
    await expect(successMessage).toBeVisible();
    await expect(successMessage).toContainText('deleted successfully');
    
    console.log('delete_individual_article_test end');
  });

  test('should confirm bulk delete actions', async ({ page }) => {
    console.log('bulk_delete_confirmation_test start');
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForLoadState('networkidle');
    
    // Open bulk delete dropdown
    await page.click('.bulk-delete-btn');
    await page.waitForTimeout(200);
    
    // Test confirmation dialog for "Delete All"
    let dialogTriggered = false;
    page.on('dialog', async dialog => {
      console.log('Bulk delete confirmation:', dialog.message());
      expect(dialog.message()).toContain('delete ALL articles');
      dialogTriggered = true;
      await dialog.dismiss(); // Dismiss to cancel the action
    });
    
    await page.click('button:has-text("Delete All")');
    await page.waitForTimeout(100);
    
    expect(dialogTriggered).toBe(true);
    
    console.log('bulk_delete_confirmation_test end');
  });

  test('should close dropdown when clicking outside', async ({ page }) => {
    console.log('dropdown_close_test start');
    
    // Navigate to news page
    await page.goto('http://localhost:8000/admin/news.php');
    await page.waitForLoadState('networkidle');
    
    // Open bulk delete dropdown
    await page.click('.bulk-delete-btn');
    await page.waitForTimeout(200);
    
    // Verify dropdown is visible
    await expect(page.locator('#bulkDropdown')).toBeVisible();
    
    // Click outside the dropdown
    await page.click('body');
    await page.waitForTimeout(200);
    
    // Verify dropdown is hidden
    await expect(page.locator('#bulkDropdown')).toBeHidden();
    
    console.log('dropdown_close_test end');
  });
});
