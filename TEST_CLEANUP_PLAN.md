# Test Cleanup for Post Management

## Overview
Implement automatic cleanup of test-created posts to prevent database pollution and ensure test isolation.

## Architecture

```mermaid
flowchart TD
    A[Test Suite Start] --> B[Run Individual Tests]
    B --> C[Track Created Posts]
    C --> D[Test Completion]
    D --> E[Global Cleanup Function]
    E --> F[Delete Test Posts]
    F --> G[Clean Database]
    
    H[Individual Test] --> I[Create Post]
    I --> J[Store Post ID/Title]
    J --> K[Test Completion]
    K --> L[Add to Cleanup List]
```

## Implementation Plan

### Phase 1: Create Cleanup Utility
- [x] Create shared cleanup function in test utilities
- [x] Track test posts created during test runs
- [x] Implement bulk delete functionality

### Phase 2: Integrate with Existing Tests
- [x] Update remove-post-php tests to track created posts
- [x] Add cleanup hooks to test suites
- [x] Ensure subtitle tests also clean up

### Phase 3: Global Test Cleanup
- [x] Add test.afterAll hooks for cleanup
- [x] Create database cleanup verification
- [x] Test the cleanup functionality

## Files to Modify
- `tests/utils/cleanup.js` - New cleanup utility
- `tests/remove-post-php.spec.js` - Add cleanup
- `tests/subtitle-feature.spec.js` - Add cleanup (when recreated)
- `playwright.config.js` - Global test configuration

## Success Criteria
1. All test-created posts are automatically deleted
2. Database remains clean after test runs
3. Tests remain isolated and repeatable
4. No manual cleanup required
