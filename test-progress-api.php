#!/usr/bin/env php
<?php
// Test the progress API directly
echo "Testing progress API...\n";

// Mock environment
if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
}

function writeProgressLog($message, $step = 0, $total = 100) {
    echo "writeProgressLog start\n";
    $logFile = 'logs/scraper-progress.log';
    $data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'step' => $step,
        'total' => $total,
        'percentage' => $total > 0 ? round(($step / $total) * 100) : 0
    ];
    file_put_contents($logFile, json_encode($data) . "\n", FILE_APPEND | LOCK_EX);
    echo "writeProgressLog end\n";
}

function getProgressLog() {
    echo "getProgressLog start\n";
    $logFile = 'logs/scraper-progress.log';
    if (!file_exists($logFile)) {
        echo "getProgressLog end - no log file\n";
        return null;
    }
    
    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if (empty($lines)) {
        echo "getProgressLog end - empty log\n";
        return null;
    }
    
    $lastLine = end($lines);
    $data = json_decode($lastLine, true);
    echo "getProgressLog end - found data\n";
    return $data;
}

// Test progress tracking logic
$progress = getProgressLog();
$outputFile = 'logs/scraper-output.log';

if (file_exists($outputFile)) {
    $output = file_get_contents($outputFile);
    echo "Output file size: " . strlen($output) . " bytes\n";
    
    // Test patterns
    $patterns = [
        'scraper run start' => 'Starting scraper process...',
        'load_existing_titles start' => 'Loading existing articles...',
        'Starting news scraping process' => 'Initializing news sources...',
        'Scraping.*from' => 'Scraping news sources...',
        'Found.*article elements' => 'Processing articles...',
        'is_valid_article_url' => 'Validating article URLs...',
        'extract_article_content' => 'Extracting article content...',
        'save_to_database' => 'Saving articles to database...',
        'SCRAPER_SUMMARY' => 'Completed!'
    ];
    
    foreach ($patterns as $pattern => $message) {
        if (preg_match("/$pattern/", $output)) {
            echo "✓ Found pattern: $pattern\n";
        } else {
            echo "✗ Missing pattern: $pattern\n";
        }
    }
    
    // Check for completion
    if (strpos($output, 'SCRAPER_SUMMARY') !== false && strpos($output, 'scraper run end') !== false) {
        echo "\n✓ Scraper has completed\n";
        
        if (preg_match('/SCRAPER_SUMMARY: found=(\d+) added=(\d+) skipped=(\d+)/', $output, $matches)) {
            $found = (int)$matches[1];
            $added = (int)$matches[2];
            $skipped = (int)$matches[3];
            echo "Summary: Found=$found, Added=$added, Skipped=$skipped\n";
            
            // Update progress to completion
            $currentProgress = getProgressLog();
            if (!$currentProgress || $currentProgress['percentage'] < 100) {
                writeProgressLog("✅ Completed! Found: {$found} | Added: {$added} | Skipped: {$skipped}", 100, 100);
                echo "✓ Updated progress to completion\n";
            } else {
                echo "✓ Progress already at completion\n";
            }
        }
    } else {
        echo "\n✗ Scraper still running or not started\n";
    }
} else {
    echo "No output file found\n";
}

// Show final progress
$finalProgress = getProgressLog();
if ($finalProgress) {
    echo "\nFinal progress:\n";
    echo "Message: " . $finalProgress['message'] . "\n";
    echo "Percentage: " . $finalProgress['percentage'] . "%\n";
} else {
    echo "\nNo progress data found\n";
}
?>
