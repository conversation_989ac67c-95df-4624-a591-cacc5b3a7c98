<?php
/**
 * Manual verification script for featured articles functionality
 */

// Set up the environment
if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "src/includes/init.php";
}

echo "Manual Verification of Featured Articles Functionality\n";
echo "======================================================\n\n";

try {
    $postManager = new Post();
    
    // Check current featured count
    $currentFeaturedCount = $postManager->getFeaturedCount();
    echo "Current featured articles count: {$currentFeaturedCount}\n\n";
    
    // Get all posts and show their featured status
    $postsResult = $postManager->getAllPosts(1, 10, false);
    $posts = $postsResult['posts'];
    
    echo "Current posts and their featured status:\n";
    echo "----------------------------------------\n";
    foreach ($posts as $post) {
        $featuredStatus = isset($post['is_featured']) && $post['is_featured'] ? '[FEATURED]' : '[NOT FEATURED]';
        echo sprintf("ID: %d | %s | %s\n", $post['id'], $featuredStatus, substr($post['title'], 0, 50));
    }
    
    echo "\n";
    
    // Test creating a new featured post (if under limit)
    if ($currentFeaturedCount < 2 && !empty($posts)) {
        echo "Testing: Creating a featured post...\n";
        
        $testTitle = "Test Featured Article - " . date('Y-m-d H:i:s');
        
        try {
            $newPostId = $postManager->createPost(
                $testTitle,
                "This is test content for a featured article.",
                false, // not draft
                1, // assuming user ID 1 exists
                "tech",
                "https://via.placeholder.com/600x400",
                "Test description for featured article",
                "https://example.com",
                "Test subtitle",
                true // featured = true
            );
            
            echo "✓ Successfully created featured post with ID: {$newPostId}\n";
            
            // Verify it's marked as featured
            $newPost = $postManager->getPost($newPostId);
            if ($newPost && $newPost['is_featured']) {
                echo "✓ Post is correctly marked as featured in database\n";
            } else {
                echo "✗ Post is not marked as featured in database\n";
            }
            
        } catch (Exception $e) {
            echo "✗ Failed to create featured post: " . $e->getMessage() . "\n";
        }
    } else {
        echo "Skipping featured post creation test (either at limit or no posts exist)\n";
    }
    
    // Test the validation when trying to exceed limit
    if ($currentFeaturedCount >= 2) {
        echo "\nTesting: Validation when exceeding featured limit...\n";
        
        try {
            $postManager->createPost(
                "Should Fail Featured Article - " . date('Y-m-d H:i:s'),
                "This should fail because we're at the featured limit.",
                false,
                1,
                "tech",
                "https://via.placeholder.com/600x400",
                "This should fail",
                "https://example.com",
                "Should fail subtitle",
                true // featured = true (should fail)
            );
            
            echo "✗ Validation failed - post was created when it shouldn't have been\n";
            
        } catch (Exception $e) {
            echo "✓ Validation correctly prevented creating post: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\nVerification complete!\n";
    echo "To test the UI:\n";
    echo "1. Start the server: php -S localhost:8000 -t public\n";
    echo "2. Visit: http://localhost:8000/admin/posts.php\n";
    echo "3. Login with admin/admin123\n";
    echo "4. Create or edit a post and check the 'Mark as featured article' checkbox\n";
    
} catch (Exception $e) {
    echo "Error during verification: " . $e->getMessage() . "\n";
    exit(1);
}
?>
