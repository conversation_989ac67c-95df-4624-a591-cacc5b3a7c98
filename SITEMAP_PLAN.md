# Dynamic XML Sitemap Generator

## Overview
Create a dynamic XML sitemap that automatically includes all published articles, category pages, and the main page. This will improve SEO by helping search engines discover and index all content.

## Architecture

```mermaid
flowchart TD
    A[/sitemap.php Request] --> B[Initialize CMS]
    B --> C[Get All Published Posts]
    C --> D[Get Valid Categories]
    D --> E[Generate XML Structure]
    E --> F{Include Main Pages}
    F --> G[Add Homepage]
    G --> H[Add Category Pages]
    H --> I[Add Individual Posts]
    I --> J[Set XML Headers]
    J --> K[Output XML Sitemap]
    
    L[Post Data] --> M[URL Generation]
    M --> N[Last Modified Date]
    N --> O[Priority & Change Freq]
    O --> I
```

## XML Sitemap Structure

```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>http://example.com/</loc>
    <lastmod>2025-06-21</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>http://example.com/tech/</loc>
    <lastmod>2025-06-21</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>http://example.com/tech/article-slug/</loc>
    <lastmod>2025-06-20</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>
</urlset>
```

## Implementation Plan

### Phase 1: Core Sitemap Generation
- [ ] Create sitemap.php file in public directory
- [ ] Set proper XML content headers
- [ ] Initialize CMS and database connection
- [ ] Create sitemap generation function

### Phase 2: Content Discovery
- [ ] Get all published posts with metadata
- [ ] Get all valid categories
- [ ] Generate URLs for all content types
- [ ] Calculate last modified dates

### Phase 3: XML Output
- [ ] Generate proper XML structure
- [ ] Set appropriate priority and change frequency
- [ ] Include all required sitemap elements
- [ ] Optimize for search engine consumption

### Phase 4: Testing
- [ ] Create Playwright test for sitemap functionality
- [ ] Verify XML structure and validity
- [ ] Test all URLs are included
- [ ] Verify proper headers and content type

## Files to Create
- `public/sitemap.php` - Main sitemap generator
- `tests/sitemap.spec.js` - Sitemap testing

## Success Criteria
1. Valid XML sitemap accessible at /sitemap.php
2. Includes homepage, category pages, and all published posts
3. Proper XML structure with required elements
4. Correct content-type headers
5. SEO-optimized priority and change frequency values
6. All URLs use SEO-friendly format
