<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GeekHub - Tech, Gaming & Cinema News</title>
    <style>
        :root {
            --primary-bg: #ffffff;
            --secondary-bg: #f8f9fa;
            --accent-bg: #e9ecef;
            --primary-text: #212529;
            --secondary-text: #6c757d;
            --accent-color: #007bff;
            --gaming-color: #28a745;
            --tech-color: #17a2b8;
            --cinema-color: #ffc107;
            --border-color: #dee2e6;
            --hover-bg: #f1f3f4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--primary-bg);
            color: var(--primary-text);
            line-height: 1.6;
            position: relative;
        }

        /* Pixel Art Background */
        .pixel-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.08;
            animation: pixelFloat 45s ease-in-out infinite;
        }

        @keyframes pixelFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-15px) rotate(1deg); }
            66% { transform: translateY(-8px) rotate(-0.5deg); }
        }

        /* Header */
        header {
            background: var(--primary-bg);
            border-bottom: 2px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 4px rgba(0,0,0,0.04);
        }

        nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
        }

        .logo {
            font-size: 2rem;
            font-weight: 800;
            color: var(--accent-color);
            text-decoration: none;
            position: relative;
        }

        .logo::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--gaming-color), var(--tech-color), var(--cinema-color));
            border-radius: 2px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 0;
        }

        .nav-links a {
            color: var(--secondary-text);
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
            border-radius: 8px;
            margin: 0 0.25rem;
        }

        .nav-links a:hover {
            color: var(--primary-text);
            background: var(--hover-bg);
        }

        /* Hero Section */
        .hero {
            max-width: 1200px;
            margin: 0 auto;
            padding: 3rem 2rem;
            text-align: center;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 5vw, 3.5rem);
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--primary-text);
        }

        .hero p {
            font-size: 1.1rem;
            color: var(--secondary-text);
            max-width: 600px;
            margin: 0 auto 2.5rem;
        }

        .category-pills {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .pill {
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s ease;
            border: 2px solid;
        }

        .pill.gaming {
            color: var(--gaming-color);
            border-color: var(--gaming-color);
        }

        .pill.tech {
            color: var(--tech-color);
            border-color: var(--tech-color);
        }

        .pill.cinema {
            color: var(--cinema-color);
            border-color: var(--cinema-color);
        }

        .pill:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .pill.gaming:hover { background: var(--gaming-color); color: white; }
        .pill.tech:hover { background: var(--tech-color); color: white; }
        .pill.cinema:hover { background: var(--cinema-color); color: white; }

        /* Main Content */
        main {
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem;
            display: block;
        }

        /* Articles */
        .articles-section h2 {
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            color: var(--primary-text);
            font-weight: 600;
        }

        .featured-article {
            background: var(--secondary-bg);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .featured-article:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        }

        .article-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: var(--secondary-text);
        }

        .category-tag {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }

        .category-tag.gaming { background: var(--gaming-color); }
        .category-tag.tech { background: var(--tech-color); }
        .category-tag.cinema { background: var(--cinema-color); }

        .featured-article h3 {
            font-size: 1.5rem;
            margin-bottom: 0.75rem;
            color: var(--primary-text);
            font-weight: 600;
        }

        .featured-article p {
            color: var(--secondary-text);
            font-size: 1rem;
            line-height: 1.6;
        }

        .articles-list {
            display: grid;
            gap: 1rem;
        }

        .article-card {
            background: var(--primary-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .article-card:hover {
            border-color: var(--accent-color);
            box-shadow: 0 4px 12px rgba(0,0,0,0.06);
        }

        .article-card h4 {
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: var(--primary-text);
            font-weight: 600;
        }

        .article-summary {
            color: var(--secondary-text);
            font-size: 0.95rem;
            line-height: 1.5;
        }

        /* Sidebar */
        .sidebar h3 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: var(--primary-text);
            font-weight: 600;
        }

        .sidebar-section {
            background: var(--secondary-bg);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }

        .trending-list {
            list-style: none;
        }

        .trending-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.95rem;
        }

        .trending-item:hover {
            color: var(--accent-color);
            padding-left: 0.5rem;
        }

        .trending-item:last-child {
            border-bottom: none;
        }

        .trending-number {
            display: inline-block;
            width: 1.5rem;
            height: 1.5rem;
            background: var(--accent-color);
            color: white;
            border-radius: 50%;
            text-align: center;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 0.75rem;
            line-height: 1.5rem;
        }

        /* Footer */
        footer {
            background: var(--secondary-bg);
            border-top: 1px solid var(--border-color);
            padding: 2rem 0 1rem;
            margin-top: 4rem;
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }

        .footer-section h4 {
            color: var(--primary-text);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .footer-links {
            list-style: none;
        }

        .footer-links a {
            color: var(--secondary-text);
            text-decoration: none;
            display: block;
            padding: 0.25rem 0;
            transition: color 0.2s ease;
        }

        .footer-links a:hover {
            color: var(--accent-color);
        }

        .footer-bottom {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
            color: var(--secondary-text);
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            main {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
        }

        @media (max-width: 768px) {
            nav {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .hero {
                padding: 2rem 1rem;
            }

            .category-pills {
                flex-direction: column;
                align-items: center;
            }

            main {
                padding: 1rem;
            }

            .footer-content {
                grid-template-columns: 1fr;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Pixel Art Background -->
    <div class="pixel-bg">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="pixelPattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
                    <!-- Gaming Controller -->
                    <g transform="translate(8, 8)">
                        <rect x="0" y="10" width="34" height="16" fill="#28a745" opacity="0.7"/>
                        <rect x="4" y="4" width="8" height="8" fill="#28a745" opacity="0.8"/>
                        <rect x="22" y="4" width="8" height="8" fill="#28a745" opacity="0.8"/>
                        <rect x="8" y="14" width="3" height="3" fill="#ffffff" opacity="0.9"/>
                        <rect x="13" y="14" width="3" height="3" fill="#ffffff" opacity="0.9"/>
                        <rect x="18" y="12" width="3" height="3" fill="#ffffff" opacity="0.9"/>
                        <rect x="23" y="16" width="3" height="3" fill="#ffffff" opacity="0.9"/>
                        <rect x="2" y="12" width="2" height="2" fill="#20c997" opacity="0.8"/>
                        <rect x="30" y="12" width="2" height="2" fill="#20c997" opacity="0.8"/>
                    </g>
                </pattern>
                
                <pattern id="pixelPattern2" x="25" y="25" width="70" height="70" patternUnits="userSpaceOnUse">
                    <!-- Retro Computer -->
                    <g transform="translate(12, 12)">
                        <rect x="0" y="25" width="46" height="22" fill="#17a2b8" opacity="0.6"/>
                        <rect x="4" y="29" width="38" height="14" fill="#ffffff" opacity="0.9"/>
                        <rect x="8" y="10" width="30" height="18" fill="#17a2b8" opacity="0.7"/>
                        <rect x="12" y="14" width="22" height="10" fill="#000000" opacity="0.4"/>
                        <rect x="18" y="0" width="10" height="14" fill="#17a2b8" opacity="0.6"/>
                        <rect x="6" y="32" width="4" height="4" fill="#fd7e14" opacity="0.8"/>
                        <rect x="36" y="32" width="4" height="4" fill="#fd7e14" opacity="0.8"/>
                        <rect x="14" y="16" width="2" height="2" fill="#20c997" opacity="0.9"/>
                        <rect x="18" y="16" width="2" height="2" fill="#20c997" opacity="0.9"/>
                        <rect x="22" y="16" width="2" height="2" fill="#20c997" opacity="0.9"/>
                    </g>
                </pattern>
                
                <pattern id="pixelPattern3" x="50" y="0" width="90" height="90" patternUnits="userSpaceOnUse">
                    <!-- Movie Camera -->
                    <g transform="translate(22, 22)">
                        <rect x="0" y="18" width="46" height="24" fill="#ffc107" opacity="0.6"/>
                        <rect x="10" y="12" width="26" height="12" fill="#ffc107" opacity="0.7"/>
                        <circle cx="23" cy="24" r="10" fill="#ffc107" opacity="0.7"/>
                        <circle cx="23" cy="24" r="6" fill="#000000" opacity="0.4"/>
                        <circle cx="23" cy="24" r="3" fill="#ffffff" opacity="0.8"/>
                        <rect x="40" y="21" width="10" height="6" fill="#ffc107" opacity="0.7"/>
                        <rect x="4" y="21" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="4" y="27" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="38" y="15" width="3" height="3" fill="#20c997" opacity="0.9"/>
                    </g>
                </pattern>
                
                <pattern id="pixelPattern4" x="0" y="60" width="45" height="45" patternUnits="userSpaceOnUse">
                    <!-- Pixel Hearts -->
                    <g transform="translate(6, 6)">
                        <rect x="8" y="4" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="20" y="4" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="4" y="8" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="12" y="8" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="16" y="8" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="24" y="8" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="8" y="12" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="12" y="12" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="16" y="12" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="20" y="12" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="12" y="16" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="16" y="16" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                        <rect x="16" y="20" width="4" height="4" fill="#e83e8c" opacity="0.8"/>
                    </g>
                </pattern>
                
                <pattern id="pixelPattern5" x="70" y="35" width="55" height="55" patternUnits="userSpaceOnUse">
                    <!-- Pixel Stars -->
                    <g transform="translate(10, 10)">
                        <rect x="16" y="4" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="12" y="8" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="16" y="8" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="20" y="8" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="4" y="12" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="8" y="12" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="12" y="12" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="16" y="12" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="20" y="12" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="24" y="12" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="28" y="12" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="12" y="16" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="16" y="16" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="20" y="16" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                        <rect x="16" y="20" width="4" height="4" fill="#6f42c1" opacity="0.7"/>
                    </g>
                </pattern>
            </defs>
            
            <rect width="100%" height="100%" fill="url(#pixelPattern)"/>
            <rect width="100%" height="100%" fill="url(#pixelPattern2)"/>
            <rect width="100%" height="100%" fill="url(#pixelPattern3)"/>
            <rect width="100%" height="100%" fill="url(#pixelPattern4)"/>
            <rect width="100%" height="100%" fill="url(#pixelPattern5)"/>
        </svg>
    </div>

    <header>
        <nav>
            <a href="#" class="logo">GeekHub</a>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#gaming">Gaming</a></li>
                <li><a href="#tech">Technology</a></li>
                <li><a href="#cinema">Cinema</a></li>
                <li><a href="#reviews">Reviews</a></li>
            </ul>
        </nav>
    </header>

    <section class="hero">
        <h1>Clean. Simple. Geeky.</h1>
        <p>Your minimalist source for gaming, technology, and cinema news. No clutter, just the stories that matter.</p>
        
        <div class="category-pills">
            <a href="#gaming" class="pill gaming">🎮 Gaming</a>
            <a href="#tech" class="pill tech">⚡ Technology</a>
            <a href="#cinema" class="pill cinema">🎬 Cinema</a>
        </div>
    </section>

    <main>
        <section class="articles-section">
            <h2>Latest Stories</h2>
            
            <article class="featured-article">
                <div class="article-meta">
                    <span class="category-tag gaming">GAMING</span>
                    <span>2 hours ago</span>
                    <span>•</span>
                    <span>Alex Chen</span>
                </div>
                <h3>The Evolution of Indie Gaming: From Bedrooms to Billions</h3>
                <p>How independent developers transformed from hobbyists into industry giants, reshaping the gaming landscape with innovative mechanics and storytelling approaches that challenge AAA conventions.</p>
            </article>

            <div class="articles-list">
                <article class="article-card">
                    <div class="article-meta">
                        <span class="category-tag tech">TECH</span>
                        <span>4 hours ago</span>
                    </div>
                    <h4>Apple's M4 Chip: The Future of Personal Computing</h4>
                    <p class="article-summary">Revolutionary architecture promises unprecedented performance per watt.</p>
                </article>

                <article class="article-card">
                    <div class="article-meta">
                        <span class="category-tag cinema">CINEMA</span>
                        <span>6 hours ago</span>
                    </div>
                    <h4>Sci-Fi Renaissance: Why Space Operas Are Back</h4>
                    <p class="article-summary">Epic space narratives are capturing audiences with mature themes.</p>
                </article>

                <article class="article-card">
                    <div class="article-meta">
                        <span class="category-tag gaming">GAMING</span>
                        <span>8 hours ago</span>
                    </div>
                    <h4>Steam Deck vs Nintendo Switch: The Handheld Wars</h4>
                    <p class="article-summary">Comprehensive comparison of portable gaming platforms.</p>
                </article>
            </div>
        </section>


    </main>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h4>GeekHub</h4>
                <ul class="footer-links">
                    <li><a href="#about">About</a></li>
                    <li><a href="#contact">Contact</a></li>
                    <li><a href="#privacy">Privacy</a></li>
                </ul>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 GeekHub. Clean. Simple. Geeky.</p>
        </div>
    </footer>

    <script>
        // Smooth scroll for category pills
        document.querySelectorAll('.pill, .nav-links a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const href = this.getAttribute('href');
                if (href.startsWith('#')) {
                    const element = document.querySelector(href);
                    if (element) {
                        element.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            });
        });

        // Simple hover animations
        document.querySelectorAll('.article-card, .trending-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(4px)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });

        // Stagger animation on load
        window.addEventListener('load', function() {
            const articles = document.querySelectorAll('.article-card');
            articles.forEach((article, index) => {
                article.style.opacity = '0';
                article.style.transform = 'translateY(10px)';
                
                setTimeout(() => {
                    article.style.transition = 'all 0.4s ease';
                    article.style.opacity = '1';
                    article.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>