<?php
// Debug script to test getPostBySlug method
if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "src/includes/init.php";
}

echo "debug_getPostBySlug start\n";

$category = 'film';
$slug = 'titre-mais-quel-titre';
$post = $postManager->getPostBySlug($category, $slug, true);

echo "Post data from getPostBySlug:\n";
print_r($post);

if (isset($post['subtitle'])) {
    echo "Subtitle found: '" . $post['subtitle'] . "'\n";
} else {
    echo "Subtitle not found in post data\n";
}

echo "debug_getPostBySlug end\n";
?>
