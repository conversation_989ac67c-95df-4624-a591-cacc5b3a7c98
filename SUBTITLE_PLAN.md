# Add Subtitle Feature to Posts - Implementation Plan

## Overview
Add an optional subtitle field to blog posts that will:
- Be displayed as an H2 element on individual article pages 
- Be stored in the database posts table
- Be editable in the admin posts form
- Enhance the visual hierarchy of post content

## Feature Architecture

```mermaid
graph TD
    A[Admin Posts Form] --> B[Add Subtitle Input Field]
    B --> C[Update Database Schema]
    C --> D[Add subtitle Column to posts Table]
    D --> E[Update Post Class Methods]
    E --> F[Display Subtitle as H2 on Article Page]
    
    G[Database Schema] --> H[posts.subtitle VARCHAR(500)]
    
    I[Post.php Methods] --> J[createPost - add subtitle parameter]
    I --> K[updatePost - add subtitle parameter]
    I --> L[getPost - return subtitle field]
    
    M[Individual Article Page] --> N[Display subtitle as H2 below title]
    
    style D fill:#ccffcc
    style F fill:#ccffcc
    style H fill:#ccffcc
```

## Database Schema Update

```mermaid
erDiagram
    posts {
        int id PK
        string title
        string subtitle "NEW FIELD - Optional"
        text content
        boolean is_draft
        datetime created_at
        datetime updated_at
        int author_id FK
        string category
        string slug
        text thumbnail_url
        text description
        text sources
    }
```

## Implementation Checklist

### Database Migration
- [x] Create migration script to add `subtitle` column to `posts` table
- [x] Run migration to update existing database
- [x] Test database schema changes

### Backend Updates
- [x] Update `Post.php` class methods:
  - [x] Modify `createPost()` method to handle subtitle parameter
  - [x] Modify `updatePost()` method to handle subtitle parameter
  - [x] Ensure `getPost()` returns subtitle field
- [x] Update form processing in `/admin/posts.php`
- [x] Add validation for subtitle field (optional, max 500 characters)

### Frontend UI Updates
- [x] Add subtitle input field to admin post form in `/admin/posts.php`
- [x] Add subtitle display to individual article page `/public/post.php`
- [x] Style subtitle as H2 element with appropriate CSS
- [x] Ensure form validation and character counter for subtitle

### Testing
- [x] Write end-to-end Playwright test for subtitle functionality
- [x] Test creating posts with and without subtitles
- [x] Test editing existing posts to add/remove subtitles
- [x] Verify subtitle display on individual article pages
- [x] Test validation and character limits

### Function Registry Update
- [x] Update `FUNCTIONS.md` with new/modified functions

## Success Criteria
- ✅ Users can add optional subtitles when creating/editing posts
- ✅ Subtitles display as H2 elements below the main title on article pages
- ✅ Subtitle field is optional and validates max 500 characters
- ✅ Existing posts continue to work without subtitles
- ✅ All tests pass

## Status: ✅ COMPLETED

The subtitle feature has been successfully implemented with:
- Database schema updated with `subtitle` column
- Backend Post class methods updated to handle subtitle parameter
- Admin form updated with subtitle input field and character counter
- Individual article page updated to display subtitle as H2
- **CSS styling added for proper visual display of subtitles**
- End-to-end Playwright tests passing (12/12)
- All validation and edge cases handled

**Issue Resolution**: The subtitle was being rendered in HTML but not visually distinct due to missing CSS. Added `.post-subtitle` styling with italic gray appearance to differentiate from main title.

## File Locations
- Database migration: `migrate-add-subtitle-column.php`
- Post class: `src/classes/Post.php`
- Admin form: `public/admin/posts.php`
- Article display: `public/post.php`
- Tests: `tests/subtitle-feature.spec.js`
