#!/usr/bin/env python3
"""
News Scraper for CMS
Scrapes news articles from various sources defined in YAML files
"""

import os
import sys
import yaml
import sqlite3
import logging
import html2text
from datetime import datetime
from urllib.parse import urljoin, urlparse
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scraper.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def clean_content(content):
    """Clean article content by removing consecutive empty lines"""
    print("clean_content start")
    if not content:
        print("clean_content end - empty content")
        return content
    
    lines = content.split('\n')
    cleaned_lines = []
    prev_empty = False
    
    for line in lines:
        is_empty = line.strip() == ''
        
        # Add line if it's not empty, or if it's empty but previous wasn't empty
        if not is_empty or not prev_empty:
            cleaned_lines.append(line)
        
        prev_empty = is_empty
    
    # Join lines and strip leading/trailing whitespace
    cleaned_content = '\n'.join(cleaned_lines).strip()
    print(f"clean_content end - cleaned {len(lines)} to {len(cleaned_lines)} lines")
    return cleaned_content

def is_valid_article_url(url, site_name):
    """Validate if URL is a real article URL and not a category or other non-article page"""
    print(f"is_valid_article_url start for {url}")
    
    if not url:
        print("is_valid_article_url end - empty URL")
        return False
    
    from urllib.parse import urlparse
    parsed = urlparse(url)
    path = parsed.path.lower()
    
    # Site-specific validation rules
    if 'techcrunch.com' in parsed.netloc.lower():
        # TechCrunch articles follow pattern: /YYYY/MM/DD/article-title/
        # Invalid patterns: /category/, /tag/, /author/, /page/
        invalid_patterns = ['/category/', '/tag/', '/author/', '/page/', '/search/']
        
        # Check for invalid patterns
        if any(pattern in path for pattern in invalid_patterns):
            print(f"is_valid_article_url end - invalid pattern found: {path}")
            return False
            
        # Check for date pattern (YYYY/MM/DD)
        import re
        date_pattern = r'/\d{4}/\d{2}/\d{2}/'
        if re.search(date_pattern, path):
            print("is_valid_article_url end - valid date pattern found")
            return True
        else:
            print(f"is_valid_article_url end - no date pattern in path: {path}")
            return False
    
    elif 'anthropic.com' in parsed.netloc.lower():
        # Anthropic articles are typically under /news/ with specific paths
        if '/news/' in path and len(path.split('/')) >= 3:
            print("is_valid_article_url end - valid Anthropic news URL")
            return True
        else:
            print(f"is_valid_article_url end - invalid Anthropic URL pattern: {path}")
            return False
    
    # Default validation for other sites - avoid common non-article patterns
    invalid_patterns = ['/category/', '/tag/', '/author/', '/page/', '/search/', '/feed/', '/rss/']
    if any(pattern in path for pattern in invalid_patterns):
        print(f"is_valid_article_url end - generic invalid pattern found: {path}")
        return False
    
    print("is_valid_article_url end - passed generic validation")
    return True

class NewsScraper:
    def __init__(self, db_path="database/cms.db"):
        self.db_path = db_path
        self.driver = None
        self.existing_titles = set()
        self.init_driver()
        self.load_existing_titles()
        
    def init_driver(self):
        """Initialize undetected Chrome driver"""
        try:
            options = uc.ChromeOptions()
            options.add_argument("--headless")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--window-size=1920,1080")
            options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36")
            
            self.driver = uc.Chrome(options=options)
            logger.info("Chrome driver initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Chrome driver: {e}")
            raise
    
    def load_yaml_configs(self, config_dir="public/admin/news_src"):
        """Load all YAML configuration files"""
        configs = []
        
        if not os.path.exists(config_dir):
            logger.error(f"Configuration directory not found: {config_dir}")
            return configs
            
        for filename in os.listdir(config_dir):
            if filename.endswith('.yaml') or filename.endswith('.yml'):
                try:
                    with open(os.path.join(config_dir, filename), 'r', encoding='utf-8') as file:
                        config = yaml.safe_load(file)
                        config['filename'] = filename
                        configs.append(config)
                        logger.info(f"Loaded config: {filename}")
                except Exception as e:
                    logger.error(f"Error loading {filename}: {e}")
                    
        return configs
    
    def scrape_site(self, config):
        """Scrape articles from a single site"""
        site_name = config.get('SITENAME', 'Unknown')
        source_url = config.get('SOURCE_URL')
        selectors = config.get('SELECTORS', {})
        
        if not source_url or not selectors:
            logger.error(f"Invalid config for {site_name}")
            return []
            
        logger.info(f"Scraping {site_name} from {source_url}")
        
        try:
            self.driver.get(source_url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Additional wait for dynamic content
            time.sleep(3)
            
            articles = []
            article_selector = selectors.get('ARTICLES')
            
            if not article_selector:
                logger.error(f"No ARTICLES selector defined for {site_name}")
                return []
                
            # Find all article elements
            article_elements = self.driver.find_elements(By.CSS_SELECTOR, article_selector)
            logger.info(f"Found {len(article_elements)} article elements")
            
            new_articles = []
            skipped_count = 0
            
            for element in article_elements[:10]:  # Limit to 10 articles per site
                try:
                    article = self.extract_basic_article_data(element, selectors, source_url, site_name)
                    if article:
                        # Check if article already exists by title
                        if self.is_article_new(article['title']):
                            new_articles.append(article)
                            # Add to existing titles to prevent duplicates in same session
                            self.add_title_to_existing(article['title'])
                        else:
                            skipped_count += 1
                            logger.info(f"Skipping existing article: {article['title'][:50]}...")
                except Exception as e:
                    logger.warning(f"Error extracting basic article: {e}")
                    continue
            
            logger.info(f"Found {len(new_articles)} new articles (skipped {skipped_count} existing)")
            
            # Now extract content for each new article using their URLs
            content_selector = selectors.get('CONTENT')
            if content_selector:
                for article in new_articles:
                    if article.get('url'):
                        article['content'] = self.extract_article_content(article['url'], content_selector)
                    else:
                        article['content'] = ''
                    
            logger.info(f"Successfully extracted {len(new_articles)} new articles from {site_name}")
            return new_articles
            
        except Exception as e:
            logger.error(f"Error scraping {site_name}: {e}")
            return []
    
    def extract_basic_article_data(self, element, selectors, base_url, site_name):
        """Extract basic data from a single article element (without content)"""
        article = {
            'site_name': site_name,
            'title': '',
            'url': '',
            'thumbnail_url': ''
        }
        
        try:
            # Extract title
            title_selector = selectors.get('TITLE')
            if title_selector:
                try:
                    title_elem = element.find_element(By.CSS_SELECTOR, title_selector)
                    # Try textContent first, then innerHTML, then text
                    article['title'] = (title_elem.get_attribute('textContent') or 
                                      title_elem.get_attribute('innerHTML') or 
                                      title_elem.text).strip()
                except NoSuchElementException:
                    # Try to get title from the main element if selector fails
                    article['title'] = (element.get_attribute('textContent') or element.text).strip()[:100] + "..."
            
            # Extract link
            link_attr = selectors.get('LINK', 'href')
            if link_attr:
                if link_attr == 'href':
                    # If the article element itself is a link
                    if element.tag_name == 'a':
                        url = element.get_attribute('href')
                    else:
                        # Find a link within the element
                        try:
                            link_elem = element.find_element(By.TAG_NAME, 'a')
                            url = link_elem.get_attribute('href')
                        except NoSuchElementException:
                            url = None
                else:
                    url = element.get_attribute(link_attr)
                
                if url:
                    full_url = urljoin(base_url, url)
                    # Validate if this is a real article URL
                    if is_valid_article_url(full_url, site_name):
                        article['url'] = full_url
                    else:
                        logger.info(f"Skipping non-article URL: {full_url}")
                        return None
            
            # Extract thumbnail
            thumbnail_selector = selectors.get('THUMBNAIL')
            if thumbnail_selector:
                try:
                    img_elem = element.find_element(By.CSS_SELECTOR, thumbnail_selector)
                    thumbnail_url = img_elem.get_attribute('src')
                    if not thumbnail_url:
                        thumbnail_url = img_elem.get_attribute('data-src')  # Lazy loading
                    if thumbnail_url:
                        article['thumbnail_url'] = urljoin(base_url, thumbnail_url)
                except NoSuchElementException:
                    pass
            
            # Validate article data
            if not article['title'] or not article['url']:
                logger.warning(f"Incomplete article data: {article}")
                return None
                
            return article
            
        except Exception as e:
            logger.error(f"Error extracting basic article data: {e}")
            return None

    def extract_article_data(self, element, selectors, base_url, site_name):
        """Extract data from a single article element"""
        article = {
            'site_name': site_name,
            'title': '',
            'url': '',
            'thumbnail_url': '',
            'content': ''
        }
        
        try:
            # Extract title
            title_selector = selectors.get('TITLE')
            if title_selector:
                try:
                    title_elem = element.find_element(By.CSS_SELECTOR, title_selector)
                    # Try textContent first, then innerHTML, then text
                    article['title'] = (title_elem.get_attribute('textContent') or 
                                      title_elem.get_attribute('innerHTML') or 
                                      title_elem.text).strip()
                except NoSuchElementException:
                    # Try to get title from the main element if selector fails
                    article['title'] = (element.get_attribute('textContent') or element.text).strip()[:100] + "..."
            
            # Extract link
            link_attr = selectors.get('LINK', 'href')
            if link_attr:
                if link_attr == 'href':
                    # If the article element itself is a link
                    if element.tag_name == 'a':
                        url = element.get_attribute('href')
                    else:
                        # Find a link within the element
                        try:
                            link_elem = element.find_element(By.TAG_NAME, 'a')
                            url = link_elem.get_attribute('href')
                        except NoSuchElementException:
                            url = None
                else:
                    url = element.get_attribute(link_attr)
                
                if url:
                    full_url = urljoin(base_url, url)
                    # Validate if this is a real article URL
                    if is_valid_article_url(full_url, site_name):
                        article['url'] = full_url
                    else:
                        logger.info(f"Skipping non-article URL: {full_url}")
                        return None
            
            # Extract thumbnail
            thumbnail_selector = selectors.get('THUMBNAIL')
            if thumbnail_selector:
                try:
                    img_elem = element.find_element(By.CSS_SELECTOR, thumbnail_selector)
                    thumbnail_url = img_elem.get_attribute('src')
                    if not thumbnail_url:
                        thumbnail_url = img_elem.get_attribute('data-src')  # Lazy loading
                    if thumbnail_url:
                        article['thumbnail_url'] = urljoin(base_url, thumbnail_url)
                except NoSuchElementException:
                    pass
            
            # Extract article content if we have a URL and content selector
            content_selector = selectors.get('CONTENT')
            if content_selector and article.get('url'):
                article['content'] = self.extract_article_content(article['url'], content_selector)
            
            # Validate article data
            if not article['title'] or not article['url']:
                logger.warning(f"Incomplete article data: {article}")
                return None
                
            return article
            
        except Exception as e:
            logger.error(f"Error extracting article data: {e}")
            return None
    
    def extract_article_content(self, article_url, content_selector):
        """Extract full article content from article page in markdown format"""
        print(f"extract_article_content start for {article_url}")
        try:
            # Navigate to article page
            self.driver.get(article_url)
            
            # Wait for content to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, content_selector))
            )
            
            # Extract content element
            content_element = self.driver.find_element(By.CSS_SELECTOR, content_selector)
            
            # Get HTML content first
            html_content = content_element.get_attribute('innerHTML')
            
            if html_content:
                # Convert HTML to markdown
                h = html2text.HTML2Text()
                h.ignore_links = False  # Keep links
                h.ignore_images = False  # Keep images
                h.body_width = 0  # Don't wrap lines
                h.protect_links = True  # Protect link formatting
                h.wrap_links = False  # Don't wrap long links
                
                markdown_content = h.handle(html_content)
                
                # Clean up consecutive empty lines
                cleaned_content = clean_content(markdown_content)
                
                print(f"extract_article_content end - extracted {len(cleaned_content)} characters in markdown")
                return cleaned_content
            else:
                # Fallback to text content if HTML is empty
                text_content = content_element.get_attribute('textContent') or content_element.text
                cleaned_content = clean_content(text_content.strip())
                print(f"extract_article_content end - extracted {len(cleaned_content)} characters as text fallback")
                return cleaned_content
            
        except TimeoutException:
            logger.warning(f"Timeout waiting for content selector {content_selector} on {article_url}")
            return ""
        except NoSuchElementException:
            logger.warning(f"Content selector {content_selector} not found on {article_url}")
            return ""
        except Exception as e:
            logger.error(f"Error extracting content from {article_url}: {e}")
            return ""

    def load_existing_titles(self):
        """Load existing article titles from database to avoid duplicates"""
        print("load_existing_titles start")
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT title FROM news_articles")
            self.existing_titles = {row[0] for row in cursor.fetchall()}
            conn.close()
            logger.info(f"Loaded {len(self.existing_titles)} existing article titles")
            print(f"load_existing_titles end - loaded {len(self.existing_titles)} titles")
        except Exception as e:
            logger.error(f"Error loading existing titles: {e}")
            self.existing_titles = set()
            print("load_existing_titles end - error occurred")
    
    def is_article_new(self, title):
        """Check if an article with this title already exists"""
        return title not in self.existing_titles
    
    def add_title_to_existing(self, title):
        """Add a title to the existing titles set to prevent re-scraping in same session"""
        self.existing_titles.add(title)

    def save_to_database(self, articles):
        """Save articles to SQLite database. Returns number of added articles."""
        if not articles:
            logger.info("No articles to save")
            return 0
        
        print("save_to_database start")
        saved_count = 0
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            for article in articles:
                try:
                    # Double-check for duplicates by title before inserting
                    cursor.execute("SELECT COUNT(*) FROM news_articles WHERE title = ?", (article['title'],))
                    if cursor.fetchone()[0] > 0:
                        logger.info(f"Article already exists in DB: {article['title'][:50]}...")
                        continue
                    
                    cursor.execute("""
                        INSERT INTO news_articles 
                        (site_name, title, url, thumbnail_url, content, scraped_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        article['site_name'],
                        article['title'],
                        article['url'],
                        article['thumbnail_url'],
                        article.get('content', ''),
                        datetime.now().isoformat()
                    ))
                    if cursor.rowcount > 0:
                        saved_count += 1
                        logger.info(f"Saved article: {article['title'][:50]}...")
                except sqlite3.IntegrityError as e:
                    logger.warning(f"Integrity error for article {article['title'][:50]}...: {e}")
                    continue
            conn.commit()
            conn.close()
            logger.info(f"Saved {saved_count} new articles to database")
            print(f"save_to_database end - saved {saved_count} articles")
            return saved_count
        except Exception as e:
            logger.error(f"Error saving to database: {e}")
            print("save_to_database end - error occurred")
            return saved_count
    
    def run(self):
        """Main scraping process"""
        print("scraper run start")
        logger.info("Starting news scraping process")
        found_count = 0
        added_count = 0
        skipped_count = 0
        try:
            # Load all configurations
            configs = self.load_yaml_configs()
            if not configs:
                logger.error("No configuration files found")
                print("SCRAPER_SUMMARY: found=0 added=0 skipped=0")
                print("scraper run end")
                return
            all_articles = []
            total_skipped = 0
            
            # Scrape each configured site
            for config in configs:
                site_name = config.get('SITENAME', 'Unknown')
                initial_existing_count = len(self.existing_titles)
                
                articles = self.scrape_site(config)
                all_articles.extend(articles)
                
                # Calculate how many were skipped for this site
                final_existing_count = len(self.existing_titles)
                site_skipped = final_existing_count - initial_existing_count - len(articles)
                total_skipped += site_skipped
                
                logger.info(f"{site_name}: {len(articles)} new articles found")
                time.sleep(2)
            
            found_count = len(all_articles)
            skipped_count = total_skipped
            
            # Save all articles to database
            added_count = self.save_to_database(all_articles)
            
            logger.info(f"Scraping completed. New articles found: {found_count}, Existing skipped: {skipped_count}, Actually saved: {added_count}")
            print(f"SCRAPER_SUMMARY: found={found_count} added={added_count} skipped={skipped_count}")
            print("scraper run end")
        except Exception as e:
            logger.error(f"Error in scraping process: {e}")
            print(f"SCRAPER_SUMMARY: found={found_count} added={added_count} skipped={skipped_count}")
            print("scraper run end")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None  # Set to None to prevent double cleanup
                logger.info("Chrome driver closed")
            except Exception as e:
                # Ignore common shutdown errors
                if "sys.meta_path is None" not in str(e):
                    logger.error(f"Error closing driver: {e}")

if __name__ == "__main__":
    import sys
    import atexit
    
    scraper = NewsScraper()
    
    # Register cleanup function to run on exit
    atexit.register(scraper.cleanup)
    
    try:
        scraper.run()
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
