# Create Post from Article Feature - Implementation Plan

## Overview
Add a "Create Post" (📝) button for each scraped article in the admin news UI. When clicked, it should mark the article as favorite AND read, then redirect to /admin/posts.php?action=new with the article's title and content prefilled in the post creation form.

## COMPLETED FEATURES ✅

### Dynamic XML Sitemap Generator ✅
- **COMPLETED**: Implemented `/public/sitemap.php` that dynamically generates SEO-friendly XML sitemap
- **COMPLETED**: Includes homepage, all valid categories, and all published posts with proper URLs  
- **COMPLETED**: Proper XML headers (`application/xml; charset=UTF-8`), lastmod dates, changefreq, and priority values
- **COMPLETED**: Error handling with fallback sitemap if generation fails
- **COMPLETED**: All 9 Playwright tests passing across Chromium, Firefox, and WebKit
- **COMPLETED**: Functions documented in FUNCTIONS.md

### Automatic Test Cleanup System ✅
- **COMPLETED**: Created `tests/utils/cleanup.js` with TestCleanup class for automated post cleanup
- **COMPLETED**: Integrated cleanup into all test suites (`remove-post-php.spec.js`, `subtitle-feature.spec.js`)
- **COMPLETED**: Added global cleanup test (`global-cleanup.spec.js`) to clean up any remaining test posts
- **COMPLETED**: Database remains clean after all tests with no manual intervention required
- **COMPLETED**: All cleanup functions documented in FUNCTIONS.md

### SEO-Friendly URLs Only (Remove post.php) ✅
- **COMPLETED**: Removed `/public/post.php` completely, enforcing SEO-friendly URLs only
- **COMPLETED**: Updated Router to redirect old `post.php?id=X` URLs to new SEO format
- **COMPLETED**: Updated all admin interfaces and test code to use only SEO URLs
- **COMPLETED**: All Playwright tests pass with proper URL handling and redirects
- **COMPLETED**: Clean 404 responses for direct `post.php` access attempts

## PENDING IMPLEMENTATION

### Create Post from Article Feature

## Feature Architecture

```mermaid
graph TD
    A[News Article Card] --> B[📝 Create Post Button]
    B --> C[AJAX Request to mark-favorite.php]
    C --> D[Mark Article as Favorite & Read in DB]
    C --> E[Return Article Data]
    E --> F[Redirect to posts.php?action=new]
    F --> G[Prefill Title & Content in Post Form]
    
    H[Database Schema] --> I[news_articles.is_favorite BOOLEAN]
    H --> J[news_articles.is_read BOOLEAN]
    
    K[API Endpoint] --> L[mark-favorite.php]
    L --> M[validateCSRF]
    L --> N[markArticleAsFavorite - Sets both favorite AND read]
    L --> O[getArticleData]
    
    style D fill:#ccffcc
    style G fill:#ccffcc
    style I fill:#ccffcc
    style J fill:#ccffcc
```

## Database Schema Update

```mermaid
erDiagram
    news_articles {
        int id PK
        string site_name
        string title
        string url
        string content
        string thumbnail_url
        datetime scraped_at
        boolean is_read
        boolean is_favorite "NEW COLUMN"
    }
```

## Implementation Checklist

### Database & Migration
- [x] Add `is_favorite` column to `news_articles` table
- [x] Create migration script: `migrate-add-favorite-column.php`
- [x] Run migration to update existing database

### API Development
- [x] Create `/admin/mark-favorite.php` API endpoint
- [x] Implement `markArticleAsFavorite($article_id)` function
- [x] **Enhanced**: Update function to mark articles as both favorite AND read
- [x] Implement `getArticleData($article_id)` function
- [x] Add CSRF protection and error handling
- [x] Return JSON response with article data
- [x] Add debug logging with error_log for console output

### Frontend UI Updates
- [x] Add "📝 Create Post" button to each article card in `/admin/news.php`
- [x] Add CSS styling for the new button (`.btn-create-post`)
- [x] Implement JavaScript function `createPostFromArticle(articleId)`
- [x] Add loading state and error handling for button
- [x] Add favorite indicator (★ Favorite) for favorited articles

### Post Creation Integration
- [x] Update `/admin/posts.php` to handle prefilled data
- [x] Modify "new" action to accept `title` and `content` URL parameters
- [x] Prefill form fields when creating new post from article

### Testing
- [x] Write Playwright end-to-end test: `create-post-from-article.spec.js`
- [x] **Enhanced**: Test verifies articles marked as both favorite AND read
- [x] Test complete user journey:
  - [x] Login to admin
  - [x] Navigate to news page
  - [x] Click "Create Post" button
  - [x] Verify article marked as favorite
  - [x] **New**: Verify article marked as read
  - [x] Verify redirect to post creation
  - [x] Verify title and content prefilled
- [x] Ensure test runs reliably with proper page verification

### Documentation
- [x] Update `FUNCTIONS.md` with new functions
- [x] Document API endpoints and parameters
- [x] Update this plan with completion status

## Function Registry Additions

Functions added to FUNCTIONS.md:
- `markArticleAsFavorite()` - Marks article as favorite in database
- `getArticleData()` - Retrieves article data for prefilling
- `createPostFromArticle()` - JavaScript function for AJAX call and redirect

## API Endpoints

### POST /admin/mark-favorite.php
**Purpose**: Mark an article as favorite and return article data for post creation

**Parameters**:
- `action`: "mark_favorite"
- `article_id`: Integer article ID
- `csrf_token`: CSRF protection token

**Response**:
```json
{
  "success": true,
  "message": "Article marked as favorite and read",
  "article": {
    "id": 123,
    "title": "Article Title",
    "content": "Article content..."
  }
}
```

## UI Components

### Create Post Button
- Location: `/admin/news.php` in article card actions
- Style: Blue button with emoji (📝 Create Post)
- Behavior: Shows loading state, calls API, redirects on success

### Favorite Indicator
- Location: Article meta section
- Display: "★ Favorite" span when `is_favorite = 1`
- Style: Gold/yellow color to indicate favorite status

## Testing Strategy

**User Goal**: As an admin, I want to quickly create a blog post from a scraped news article with the title and content pre-filled, and have the article automatically marked as both favorite and read since I've processed it.

**Success Criteria**: 
1. Click "📝 Create Post" button on any article
2. Article is marked as favorite AND read in database
3. Redirected to post creation page with prefilled title and content
4. Can successfully save the post

**Playwright Test Coverage**:
- Authentication and navigation
- UI element verification and interaction
- API functionality (marking as favorite AND read)
- Database integration (favorite and read status persistence)
- Form prefilling and data integrity
- Cross-page navigation and state management

## Status: ✅ COMPLETED

All features have been implemented and tested successfully. The end-to-end Playwright test passes, confirming:
- ✅ UI components are properly rendered
- ✅ API endpoints function correctly  
- ✅ Database integration works
- ✅ Post creation prefilling works
- ✅ Favorite marking persists correctly