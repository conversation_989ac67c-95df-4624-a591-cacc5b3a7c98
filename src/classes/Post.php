<?php
/**
 * Post Management Class
 * Handles CRUD operations for blog posts
 */

class Post
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Get valid categories
     */
    public static function getValidCategories()
    {
        return ["tech", "gaming", "film", "serie"];
    }

    /**
     * Get all posts with pagination
     */
    public function getAllPosts(
        $page = 1,
        $perPage = 10,
        $publishedOnly = false
    ) {
        $offset = ($page - 1) * $perPage;

        $whereClause = $publishedOnly ? "WHERE is_draft = 0" : "";

        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM posts " . $whereClause;
        $total = $this->db->fetchOne($totalQuery)["total"];

        // Get posts
        $postsQuery = "
            SELECT p.*, u.username as author_name
            FROM posts p
            JOIN users u ON p.author_id = u.id
            {$whereClause}
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $posts = $this->db->fetchAll($postsQuery, [$perPage, $offset]);

        return [
            "posts" => $posts,
            "total" => $total,
            "page" => $page,
            "perPage" => $perPage,
            "totalPages" => ceil($total / $perPage),
        ];
    }

    /**
     * Get posts by category with pagination
     */
    public function getPostsByCategory(
        $category,
        $page = 1,
        $perPage = 10,
        $publishedOnly = false
    ) {
        $offset = ($page - 1) * $perPage;

        // Validate category
        $validCategories = self::getValidCategories();
        if (!in_array($category, $validCategories)) {
            throw new Exception("Invalid category specified.");
        }

        $whereClause = $publishedOnly
            ? "WHERE p.category = ? AND is_draft = 0"
            : "WHERE p.category = ?";

        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM posts p " . $whereClause;
        $total = $this->db->fetchOne($totalQuery, [$category])["total"];

        // Get posts
        $postsQuery = "
            SELECT p.*, u.username as author_name
            FROM posts p
            JOIN users u ON p.author_id = u.id
            {$whereClause}
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $posts = $this->db->fetchAll($postsQuery, [
            $category,
            $perPage,
            $offset,
        ]);

        return [
            "posts" => $posts,
            "total" => $total,
            "page" => $page,
            "perPage" => $perPage,
            "totalPages" => ceil($total / $perPage),
            "category" => $category,
        ];
    }

    /**
     * Get single post by ID
     */
    public function getPost($id, $publishedOnly = false)
    {
        $whereClause = $publishedOnly ? "AND is_draft = 0" : "";

        $query = "
            SELECT p.*, u.username as author_name
            FROM posts p
            JOIN users u ON p.author_id = u.id
            WHERE p.id = ? {$whereClause}
        ";

        return $this->db->fetchOne($query, [$id]);
    }

    /**
     * Get single post by category and slug
     */
    public function getPostBySlug($category, $slug, $publishedOnly = false)
    {
        $whereClause = $publishedOnly ? "AND is_draft = 0" : "";

        $query = "
            SELECT p.*, u.username as author_name
            FROM posts p
            JOIN users u ON p.author_id = u.id
            WHERE p.category = ? AND p.slug = ? {$whereClause}
        ";

        return $this->db->fetchOne($query, [$category, $slug]);
    }

    /**
     * Create new post
     */
    public function createPost(
        $title,
        $content,
        $isDraft = true,
        $authorId = null,
        $category = "tech",
        $thumbnailUrl = null,
        $description = null,
        $sources = null,
        $subtitle = null,
        $isFeatured = false
    ) {
        // Validate input
        if (!Security::validateLength($title, 1, 255)) {
            throw new Exception("Title must be between 1 and 255 characters.");
        }

        if (!Security::validateLength($content, 1, 65535)) {
            throw new Exception(
                "Content is required and must not exceed 65535 characters."
            );
        }

        // Validate description - now required
        if (empty($description)) {
            throw new Exception("Description is required.");
        }
        if (!Security::validateLength($description, 1, 500)) {
            throw new Exception(
                "Description must be between 1 and 500 characters."
            );
        }

        // Validate thumbnail URL - now required
        if (empty($thumbnailUrl)) {
            throw new Exception("Thumbnail is required.");
        }

        // Validate sources if provided
        if ($sources !== null && !empty($sources)) {
            $sources = $this->validateSources($sources);
        }

        // Validate subtitle if provided
        if ($subtitle !== null && !empty($subtitle)) {
            if (!Security::validateLength($subtitle, 1, 500)) {
                throw new Exception(
                    "Subtitle must not exceed 500 characters."
                );
            }
        }

        // Validate featured status (maximum 2 featured posts)
        $this->validateFeaturedStatus($isFeatured);

        // Check if title already exists
        $existingPost = $this->db->fetchOne(
            "SELECT id FROM posts WHERE title = ?",
            [$title]
        );
        if ($existingPost) {
            throw new Exception(
                "A post with this title already exists. Please choose a different title."
            );
        }

        // Validate category
        $validCategories = ["tech", "gaming", "film", "serie"];
        if (!in_array($category, $validCategories)) {
            throw new Exception(
                "Invalid category. Must be one of: " .
                    implode(", ", $validCategories)
            );
        }

        // Use current user if no author specified
        if ($authorId === null) {
            $authorId = Session::getUserId();
            if (!$authorId) {
                throw new Exception("Author ID is required.");
            }
        }

        // Sanitize input (but not content - keep markdown as plain text)
        $title = Security::sanitizeInput($title);
        $content = trim($content); // Only trim content, don't HTML encode
        $category = Security::sanitizeInput($category);
        $description = $description
            ? Security::sanitizeInput($description)
            : null;
        $sources = $sources ? Security::sanitizeInput($sources) : null;
        $subtitle = $subtitle ? Security::sanitizeInput($subtitle) : null;

        // Validate and sanitize thumbnail URL
        if ($thumbnailUrl && !self::isValidThumbnailUrl($thumbnailUrl)) {
            throw new Exception("Invalid thumbnail URL format.");
        }
        $thumbnailUrl = $thumbnailUrl
            ? Security::sanitizeInput($thumbnailUrl)
            : null;

        // Generate unique slug
        $slug = generateUniqueSlug($title);

        // Insert post
        $this->db->query(
            "INSERT INTO posts (title, subtitle, content, is_draft, author_id, category, slug, thumbnail_url, description, sources, is_featured) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $title,
                $subtitle,
                $content,
                $isDraft ? 1 : 0,
                $authorId,
                $category,
                $slug,
                $thumbnailUrl,
                $description,
                $sources,
                $isFeatured ? 1 : 0,
            ]
        );

        return $this->db->lastInsertId();
    }

    /**
     * Update existing post
     */
    public function updatePost(
        $id,
        $title,
        $content,
        $isDraft = null,
        $category = null,
        $thumbnailUrl = null,
        $description = null,
        $sources = null,
        $subtitle = null,
        $isFeatured = null
    ) {
        // Validate input
        if (!Security::validateLength($title, 1, 255)) {
            throw new Exception("Title must be between 1 and 255 characters.");
        }

        if (!Security::validateLength($content, 1, 65535)) {
            throw new Exception(
                "Content is required and must not exceed 65535 characters."
            );
        }

        // Validate description - now required
        if ($description !== null) {
            if (empty($description)) {
                throw new Exception("Description is required.");
            }
            if (!Security::validateLength($description, 1, 500)) {
                throw new Exception(
                    "Description must be between 1 and 500 characters."
                );
            }
        }

        // Validate sources if provided
        if ($sources !== null && !empty($sources)) {
            $sources = $this->validateSources($sources);
        }

        // Validate subtitle if provided
        if ($subtitle !== null && !empty($subtitle)) {
            if (!Security::validateLength($subtitle, 1, 500)) {
                throw new Exception(
                    "Subtitle must not exceed 500 characters."
                );
            }
        }

        // Validate category if provided
        if ($category !== null) {
            $validCategories = ["tech", "gaming", "film", "serie"];
            if (!in_array($category, $validCategories)) {
                throw new Exception(
                    "Invalid category. Must be one of: " .
                        implode(", ", $validCategories)
                );
            }
        }

        // Check if post exists
        $existingPost = $this->getPost($id);
        if (!$existingPost) {
            throw new Exception("Post not found.");
        }

        // Check if title already exists (excluding current post)
        if ($title !== $existingPost["title"]) {
            $duplicatePost = $this->db->fetchOne(
                "SELECT id FROM posts WHERE title = ? AND id != ?",
                [$title, $id]
            );
            if ($duplicatePost) {
                throw new Exception(
                    "A post with this title already exists. Please choose a different title."
                );
            }
        }

        // Sanitize input (but not content - keep markdown as plain text)
        $title = Security::sanitizeInput($title);
        $content = trim($content); // Only trim content, don't HTML encode
        if ($category !== null) {
            $category = Security::sanitizeInput($category);
        }
        if ($description !== null) {
            $description = $description
                ? Security::sanitizeInput($description)
                : null;
        }
        if ($sources !== null) {
            $sources = $sources ? Security::sanitizeInput($sources) : null;
        }
        if ($subtitle !== null) {
            $subtitle = $subtitle ? Security::sanitizeInput($subtitle) : null;
        }

        // Validate featured status if being updated
        if ($isFeatured !== null) {
            $this->validateFeaturedStatus($isFeatured, $id);
        }

        // Validate and sanitize thumbnail URL - now required when provided
        if ($thumbnailUrl !== null) {
            if (empty($thumbnailUrl)) {
                throw new Exception("Thumbnail is required.");
            }
            if (!self::isValidThumbnailUrl($thumbnailUrl)) {
                throw new Exception("Invalid thumbnail URL format.");
            }
            $thumbnailUrl = Security::sanitizeInput($thumbnailUrl);
        }

        // Generate new slug if title changed
        $updateSlug = false;
        $newSlug = null;
        if ($title !== $existingPost["title"]) {
            $newSlug = generateUniqueSlug($title, $id);
            $updateSlug = true;
        }

        // Build update query
        $params = [$title, $content];
        $query =
            "UPDATE posts SET title = ?, content = ?, updated_at = CURRENT_TIMESTAMP";

        if ($isDraft !== null) {
            $query .= ", is_draft = ?";
            $params[] = $isDraft ? 1 : 0;
        }

        if ($category !== null) {
            $query .= ", category = ?";
            $params[] = $category;
        }

        if ($updateSlug) {
            $query .= ", slug = ?";
            $params[] = $newSlug;
        }

        if ($thumbnailUrl !== null) {
            $query .= ", thumbnail_url = ?";
            $params[] = $thumbnailUrl;
        }

        if ($description !== null) {
            $query .= ", description = ?";
            $params[] = $description;
        }

        if ($sources !== null) {
            $query .= ", sources = ?";
            $params[] = $sources;
        }

        if ($subtitle !== null) {
            $query .= ", subtitle = ?";
            $params[] = $subtitle;
        }

        if ($isFeatured !== null) {
            $query .= ", is_featured = ?";
            $params[] = $isFeatured ? 1 : 0;
        }

        $query .= " WHERE id = ?";
        $params[] = $id;

        $this->db->query($query, $params);

        return true;
    }

    /**
     * Validate sources URLs
     */
    public function validateSources($sources)
    {
        if (empty($sources)) {
            return null;
        }

        $urls = array_map("trim", explode(",", $sources));
        $validUrls = [];

        foreach ($urls as $url) {
            if (empty($url)) {
                continue;
            }

            // Basic URL validation
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                throw new Exception("Invalid URL format: {$url}");
            }

            $validUrls[] = $url;
        }

        return empty($validUrls) ? null : implode(", ", $validUrls);
    }

    /**
     * Calculate reading time based on word count
     * Average adult reading speed: 250 words per minute
     */
    public function calculateReadingTime($content)
    {
        error_log("[calculateReadingTime] start");

        if (empty($content)) {
            error_log("[calculateReadingTime] end - empty content");
            return 1;
        }

        // Remove HTML tags and count words
        $plainText = strip_tags($content);
        $wordCount = str_word_count($plainText);

        // Calculate reading time (250 words per minute average)
        $readingTime = ceil($wordCount / 250);

        // Minimum 1 minute
        $readingTime = max(1, $readingTime);

        error_log(
            "[calculateReadingTime] end - {$wordCount} words = {$readingTime} min"
        );
        return $readingTime;
    }

    /**
     * Format sources for display
     */
    public function formatSources($sources)
    {
        error_log("[formatSources] start");

        if (empty($sources)) {
            error_log("[formatSources] end - no sources");
            return [];
        }

        $urls = array_map("trim", explode(",", $sources));
        $formattedSources = [];

        foreach ($urls as $url) {
            if (!empty($url)) {
                $domain = parse_url($url, PHP_URL_HOST);
                $formattedSources[] = [
                    "url" => Security::escape($url),
                    "domain" => Security::escape($domain ?: $url),
                ];
            }
        }

        error_log(
            "[formatSources] end - formatted " .
                count($formattedSources) .
                " sources"
        );
        return $formattedSources;
    }

    /**
     * Delete post
     */
    public function deletePost($id)
    {
        // Check if post exists
        $post = $this->getPost($id);
        if (!$post) {
            throw new Exception("Post not found.");
        }

        $this->db->query("DELETE FROM posts WHERE id = ?", [$id]);

        return true;
    }

    /**
     * Toggle post draft status
     */
    public function toggleDraftStatus($id)
    {
        $post = $this->getPost($id);
        if (!$post) {
            throw new Exception("Post not found.");
        }

        $newStatus = $post["is_draft"] ? 0 : 1;
        $this->db->query(
            "UPDATE posts SET is_draft = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            [$newStatus, $id]
        );

        return $newStatus;
    }

    /**
     * Get recent posts for dashboard
     */
    public function getRecentPosts($limit = 5)
    {
        return $this->db->fetchAll(
            "SELECT p.*, u.username as author_name
             FROM posts p
             JOIN users u ON p.author_id = u.id
             ORDER BY p.updated_at DESC
             LIMIT ?",
            [$limit]
        );
    }

    /**
     * Get post statistics
     */
    public function getStatistics()
    {
        $stats = [];

        // Total posts
        $stats["total"] = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM posts"
        )["count"];

        // Published posts
        $stats["published"] = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM posts WHERE is_draft = 0"
        )["count"];

        // Draft posts
        $stats["drafts"] = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM posts WHERE is_draft = 1"
        )["count"];

        return $stats;
    }

    /**
     * Search posts
     */
    public function searchPosts(
        $query,
        $publishedOnly = false,
        $page = 1,
        $perPage = 10
    ) {
        $offset = ($page - 1) * $perPage;
        $searchTerm = "%" . $query . "%";

        $whereClause = "WHERE (title LIKE ? OR content LIKE ?)";
        if ($publishedOnly) {
            $whereClause .= " AND is_draft = 0";
        }

        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM posts " . $whereClause;
        $total = $this->db->fetchOne($totalQuery, [$searchTerm, $searchTerm])[
            "total"
        ];

        // Get posts
        $postsQuery = "
            SELECT p.*, u.username as author_name
            FROM posts p
            JOIN users u ON p.author_id = u.id
            {$whereClause}
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
        ";

        $posts = $this->db->fetchAll($postsQuery, [
            $searchTerm,
            $searchTerm,
            $perPage,
            $offset,
        ]);

        return [
            "posts" => $posts,
            "total" => $total,
            "page" => $page,
            "perPage" => $perPage,
            "totalPages" => ceil($total / $perPage),
            "query" => $query,
        ];
    }

    /**
     * Get count of featured posts
     */
    public function getFeaturedCount()
    {
        $result = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM posts WHERE is_featured = 1"
        );
        return $result ? $result["count"] : 0;
    }

    /**
     * Get featured posts
     */
    public function getFeaturedPosts($limit = 2)
    {
        return $this->db->fetchAll(
            "SELECT p.*, u.username as author_name
             FROM posts p
             JOIN users u ON p.author_id = u.id
             WHERE p.is_featured = 1 AND p.is_draft = 0
             ORDER BY p.created_at DESC
             LIMIT ?",
            [$limit]
        );
    }

    /**
     * Validate featured status - maximum 2 featured posts allowed
     */
    private function validateFeaturedStatus($isFeatured, $excludePostId = null)
    {
        if (!$isFeatured) {
            return true; // Not setting as featured, no validation needed
        }

        $query = "SELECT COUNT(*) as count FROM posts WHERE is_featured = 1";
        $params = [];

        if ($excludePostId !== null) {
            $query .= " AND id != ?";
            $params[] = $excludePostId;
        }

        $result = $this->db->fetchOne($query, $params);
        $currentFeaturedCount = $result ? $result["count"] : 0;

        if ($currentFeaturedCount >= 2) {
            throw new Exception("Maximum of 2 featured articles allowed. Please unfeatured another article first.");
        }

        return true;
    }

    /**
     * Validate thumbnail URL - accepts both full URLs and relative paths
     */
    private static function isValidThumbnailUrl($url)
    {
        // Allow relative paths starting with /uploads/
        if (strpos($url, "/uploads/") === 0) {
            return true;
        }

        // Allow full valid URLs
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
}
?>
