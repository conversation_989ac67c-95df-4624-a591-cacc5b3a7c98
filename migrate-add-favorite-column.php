<?php
/**
 * Database migration to add is_favorite column to news_articles table
 */

echo "add_favorite_column_migration start\n";

try {
    $db_path = __DIR__ . '/database/cms.db';
    $conn = new PDO("sqlite:$db_path");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if column already exists
    $stmt = $conn->prepare("PRAGMA table_info(news_articles)");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $has_favorite_column = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'is_favorite') {
            $has_favorite_column = true;
            break;
        }
    }
    
    if (!$has_favorite_column) {
        echo "Adding is_favorite column to news_articles table...\n";
        $conn->exec("ALTER TABLE news_articles ADD COLUMN is_favorite INTEGER DEFAULT 0");
        echo "✓ is_favorite column added successfully\n";
    } else {
        echo "✓ is_favorite column already exists\n";
    }
    
    echo "add_favorite_column_migration end\n";
    
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
