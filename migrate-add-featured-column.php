<?php
/**
 * Database migration to add is_featured column to posts table
 */

echo "add_featured_column_migration start\n";

try {
    $db_path = __DIR__ . '/database/cms.db';
    $conn = new PDO("sqlite:$db_path");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if column already exists
    $stmt = $conn->prepare("PRAGMA table_info(posts)");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $has_featured_column = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'is_featured') {
            $has_featured_column = true;
            break;
        }
    }
    
    if (!$has_featured_column) {
        echo "Adding is_featured column to posts table...\n";
        $conn->exec("ALTER TABLE posts ADD COLUMN is_featured INTEGER DEFAULT 0");
        echo "✓ is_featured column added successfully\n";
    } else {
        echo "✓ is_featured column already exists\n";
    }
    
    echo "add_featured_column_migration end\n";
    
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
