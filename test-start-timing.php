#!/usr/bin/env php
<?php
// Test the progress API timing directly
echo "Testing scraper start API timing...\n";

// Clear previous logs
if (file_exists('logs/scraper-progress.log')) {
    unlink('logs/scraper-progress.log');
}
if (file_exists('logs/scraper-output.log')) {
    unlink('logs/scraper-output.log');
}

// Create a test session
session_start();
$_SESSION['user_id'] = 1; // Simulate logged in user

// Mock the auth object
$auth = new stdClass();
$auth->requireAuth = function() {
    // Mock - no authentication required for test
};

// Mock CSRF validation
function validateCSRF() {
    // Mock - no CSRF required for test
}

// Mock the CMS_INIT
if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
}

// Test the progress functions directly
function writeProgressLog($message, $step = 0, $total = 100) {
    echo "writeProgressLog start\n";
    $logFile = 'logs/scraper-progress.log';
    $data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'step' => $step,
        'total' => $total,
        'percentage' => $total > 0 ? round(($step / $total) * 100) : 0
    ];
    file_put_contents($logFile, json_encode($data) . "\n", FILE_APPEND | LOCK_EX);
    echo "writeProgressLog end\n";
}

function clearProgressLog() {
    echo "clearProgressLog start\n";
    $logFile = 'logs/scraper-progress.log';
    if (file_exists($logFile)) {
        unlink($logFile);
    }
    echo "clearProgressLog end\n";
}

// Test the start action timing
$startTime = microtime(true);

echo "Testing start action...\n";

// Clear previous progress log
clearProgressLog();

// Initialize progress
writeProgressLog("Initializing scraper...", 0, 100);

// Start scraper in background using proper background execution
$workingDir = __DIR__;
$command = "cd " . escapeshellarg($workingDir) . " && python3 scraper.py > logs/scraper-output.log 2>&1 &";

echo "Executing command: $command\n";

// Use shell_exec with nohup for true background execution
$fullCommand = "nohup bash -c " . escapeshellarg($command) . " > /dev/null 2>&1 &";
shell_exec($fullCommand);

// Give it a moment to start
usleep(100000); // 0.1 seconds

writeProgressLog("Scraper started...", 10, 100);

$endTime = microtime(true);
$duration = ($endTime - $startTime) * 1000; // Convert to milliseconds

echo "Start action completed in: " . round($duration, 2) . " ms\n";

if ($duration < 1000) {
    echo "✓ PASS: Start action returns quickly (under 1 second)\n";
} else {
    echo "✗ FAIL: Start action took too long\n";
}

// Check if scraper process actually started
sleep(1);
if (file_exists('logs/scraper-output.log')) {
    echo "✓ PASS: Scraper output file created\n";
} else {
    echo "✗ FAIL: Scraper output file not created\n";
}

echo "Test completed.\n";
?>
