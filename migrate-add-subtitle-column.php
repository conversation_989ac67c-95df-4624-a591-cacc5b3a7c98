<?php
/**
 * Database migration to add subtitle column to posts table
 */

echo "add_subtitle_column_migration start\n";

try {
    $db_path = __DIR__ . '/database/cms.db';
    $conn = new PDO("sqlite:$db_path");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if column already exists
    $stmt = $conn->prepare("PRAGMA table_info(posts)");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $has_subtitle_column = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'subtitle') {
            $has_subtitle_column = true;
            break;
        }
    }
    
    if (!$has_subtitle_column) {
        echo "Adding subtitle column to posts table...\n";
        $conn->exec("ALTER TABLE posts ADD COLUMN subtitle VARCHAR(500) DEFAULT NULL");
        echo "✓ subtitle column added successfully\n";
    } else {
        echo "✓ subtitle column already exists\n";
    }
    
    echo "add_subtitle_column_migration end\n";
    
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
