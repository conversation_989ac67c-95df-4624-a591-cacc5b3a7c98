<?php
/**
 * News Management Page
 * Display scraped news articles and manage scraping
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "../../src/includes/init.php";
}

// Require authentication
$auth->requireAuth();

$currentUser = $auth->getCurrentUser();
$action = $_GET["action"] ?? "list";

// Handle source management actions
if ($action === "sources") {
    // Handle source actions
    if ($_SERVER["REQUEST_METHOD"] === "POST") {
        try {
            validateCSRF();
            
            if (isset($_POST["create_source"])) {
                $sourceName = trim($_POST["source_name"]);
                $sourceUrl = trim($_POST["source_url"]);
                $articlesSelector = trim($_POST["articles_selector"]);
                $titleSelector = trim($_POST["title_selector"]);
                $thumbnailSelector = trim($_POST["thumbnail_selector"]);
                $contentSelector = trim($_POST["content_selector"]);
                
                if (empty($sourceName) || empty($sourceUrl) || empty($articlesSelector)) {
                    throw new Exception("Source name, URL and articles selector are required.");
                }
                
                // Create YAML content
                $yamlContent = [
                    'SITENAME' => $sourceName,
                    'SOURCE_URL' => $sourceUrl,
                    'SELECTORS' => [
                        'ARTICLES' => $articlesSelector,
                        'TITLE' => $titleSelector,
                        'THUMBNAIL' => $thumbnailSelector ?: 'img',
                        'LINK' => 'href',
                        'CONTENT' => $contentSelector ?: ''
                    ]
                ];
                
                // Generate filename
                $filename = strtolower(preg_replace('/[^a-zA-Z0-9]/', '_', $sourceName)) . '.yaml';
                $filepath = dirname(__DIR__, 2) . "/public/admin/news_src/" . $filename;
                
                if (file_exists($filepath)) {
                    throw new Exception("A source with this name already exists.");
                }
                
                // Write YAML file
                $yamlString = generateSimpleYaml($yamlContent);
                if (file_put_contents($filepath, $yamlString) === false) {
                    throw new Exception("Failed to create source file.");
                }
                
                addFlashMessage("Source '{$sourceName}' created successfully.", "success");
                redirect("news.php?action=sources");
            }
            
            if (isset($_POST["delete_source"])) {
                $filename = $_POST["filename"];
                $filepath = dirname(__DIR__, 2) . "/public/admin/news_src/" . $filename;
                
                if (!file_exists($filepath) || !str_ends_with($filename, '.yaml')) {
                    throw new Exception("Invalid source file.");
                }
                
                if (unlink($filepath)) {
                    addFlashMessage("Source deleted successfully.", "success");
                } else {
                    throw new Exception("Failed to delete source file.");
                }
                
                redirect("news.php?action=sources");
            }
            
            if (isset($_POST["update_source"])) {
                $filename = $_POST["filename"];
                $sourceName = trim($_POST["source_name"]);
                $sourceUrl = trim($_POST["source_url"]);
                $articlesSelector = trim($_POST["articles_selector"]);
                $titleSelector = trim($_POST["title_selector"]);
                $thumbnailSelector = trim($_POST["thumbnail_selector"]);
                $contentSelector = trim($_POST["content_selector"]);
                
                $filepath = dirname(__DIR__, 2) . "/public/admin/news_src/" . $filename;
                
                if (!file_exists($filepath) || !str_ends_with($filename, '.yaml')) {
                    throw new Exception("Invalid source file.");
                }
                
                if (empty($sourceName) || empty($sourceUrl) || empty($articlesSelector)) {
                    throw new Exception("Source name, URL and articles selector are required.");
                }
                
                // Create YAML content
                $yamlContent = [
                    'SITENAME' => $sourceName,
                    'SOURCE_URL' => $sourceUrl,
                    'SELECTORS' => [
                        'ARTICLES' => $articlesSelector,
                        'TITLE' => $titleSelector,
                        'THUMBNAIL' => $thumbnailSelector ?: 'img',
                        'LINK' => 'href',
                        'CONTENT' => $contentSelector ?: ''
                    ]
                ];
                
                // Write YAML file
                $yamlString = generateSimpleYaml($yamlContent);
                if (file_put_contents($filepath, $yamlString) === false) {
                    throw new Exception("Failed to update source file.");
                }
                
                addFlashMessage("Source '{$sourceName}' updated successfully.", "success");
                redirect("news.php?action=sources");
            }
            
        } catch (Exception $e) {
            addFlashMessage($e->getMessage(), "error");
            redirect("news.php?action=sources");
        }
    }
}

// Function to build redirect URL preserving current filter and page
function buildNewsRedirectUrl() {
    $redirectUrl = "news.php";
    $params = [];
    if (isset($_GET["filter"]) && $_GET["filter"] !== "all") {
        $params["filter"] = $_GET["filter"];
    }
    if (isset($_GET["page"]) && $_GET["page"] > 1) {
        $params["page"] = $_GET["page"];
    }
    if (!empty($params)) {
        $redirectUrl .= "?" . http_build_query($params);
    }
    return $redirectUrl;
}

// Function to load sources from YAML files
function loadSources() {
    $sourcesDir = dirname(__DIR__, 2) . "/public/admin/news_src/";
    $sources = [];
    
    if (!is_dir($sourcesDir)) {
        return $sources;
    }
    
    $files = glob($sourcesDir . "*.yaml");
    foreach ($files as $file) {
        $filename = basename($file);
        $content = file_get_contents($file);
        
        try {
            $config = parseSimpleYaml($content);
            if ($config && isset($config['SITENAME'])) {
                $sources[] = [
                    'filename' => $filename,
                    'name' => $config['SITENAME'],
                    'url' => $config['SOURCE_URL'] ?? '',
                    'selectors' => $config['SELECTORS'] ?? [],
                    'last_modified' => filemtime($file)
                ];
            }
        } catch (Exception $e) {
            // Skip invalid YAML files
            continue;
        }
    }
    
    return $sources;
}

// Simple YAML parser for our specific format
function parseSimpleYaml($content) {
    $lines = explode("\n", $content);
    $result = [];
    $currentKey = null;
    $inSelectors = false;
    
    foreach ($lines as $line) {
        $originalLine = $line;
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) continue;
        
        if (strpos($line, 'SITENAME:') === 0) {
            $result['SITENAME'] = trim(substr($line, 9));
        } elseif (strpos($line, 'SOURCE_URL:') === 0) {
            $result['SOURCE_URL'] = trim(substr($line, 11));
        } elseif (strpos($line, 'SELECTORS:') === 0) {
            $result['SELECTORS'] = [];
            $inSelectors = true;
        } elseif ($inSelectors && strpos($originalLine, '  ') === 0) {
            $parts = explode(':', $line, 2);
            if (count($parts) === 2) {
                $key = trim($parts[0]);
                $value = trim($parts[1]);
                // Remove quotes if present
                $value = trim($value, '"\'');
                // Remove comments
                if (strpos($value, '#') !== false) {
                    $value = trim(explode('#', $value)[0]);
                }
                $result['SELECTORS'][$key] = $value;
            }
        }
    }
    
    return $result;
}

// Simple YAML generator
function generateSimpleYaml($data) {
    $yaml = "SITENAME: " . $data['SITENAME'] . "\n";
    $yaml .= "SOURCE_URL: " . $data['SOURCE_URL'] . "\n";
    $yaml .= "SELECTORS:\n";
    
    foreach ($data['SELECTORS'] as $key => $value) {
        $yaml .= "  $key: '$value'\n";
    }
    
    return $yaml;
}

// Handle actions
if ($_SERVER["REQUEST_METHOD"] === "POST" && $action === "list") {
    try {
        validateCSRF();
        
        if (isset($_POST["scrape_now"])) {
            // Run scraper
            $command = "cd " . escapeshellarg(dirname(__DIR__, 2)) . " && python3 scraper.py";
            $output = shell_exec($command . " 2>&1");
            $found = null;
            $added = null;
            $skipped = null;
            if ($output) {
                // Try to extract summary line with skipped count
                if (preg_match('/SCRAPER_SUMMARY: found=(\d+) added=(\d+) skipped=(\d+)/', $output, $matches)) {
                    $found = (int)$matches[1];
                    $added = (int)$matches[2];
                    $skipped = (int)$matches[3];
                    addFlashMessage("✅ Scraping completed! Found: {$found} new articles | Added: {$added} | Skipped: {$skipped} existing", "success");
                } else if (preg_match('/SCRAPER_SUMMARY: found=(\d+) added=(\d+)/', $output, $matches)) {
                    // Fallback for old format
                    $found = (int)$matches[1];
                    $added = (int)$matches[2];
                    addFlashMessage("✅ Scraping completed! Found: {$found} articles | Added: {$added} new articles", "success");
                } else {
                    addFlashMessage("Scraping completed. Check logs for details.", "success");
                }
            } else {
                addFlashMessage("Scraping started in background.", "info");
            }
            redirect("news.php");
        }
        
        if (isset($_POST["mark_read"])) {
            $articleId = (int) $_POST["article_id"];
            $db = Database::getInstance()->getPdo();
            $stmt = $db->prepare("UPDATE news_articles SET is_read = 1 WHERE id = ?");
            $stmt->execute([$articleId]);
            
            addFlashMessage("Article marked as read.", "success");
            redirect(buildNewsRedirectUrl());
        }
        
        if (isset($_POST["toggle_favorite"])) {
            $articleId = (int) $_POST["article_id"];
            $db = Database::getInstance()->getPdo();
            
            // Get current favorite status
            $stmt = $db->prepare("SELECT is_favorite FROM news_articles WHERE id = ?");
            $stmt->execute([$articleId]);
            $current = $stmt->fetchColumn();
            
            // Toggle favorite status
            $newStatus = $current ? 0 : 1;
            $stmt = $db->prepare("UPDATE news_articles SET is_favorite = ? WHERE id = ?");
            $stmt->execute([$newStatus, $articleId]);
            
            $message = $newStatus ? "Added to favorites" : "Removed from favorites";
            addFlashMessage($message, "success");
            redirect(buildNewsRedirectUrl());
        }
        
        if (isset($_POST["delete_article"])) {
            error_log("delete_article start");
            $articleId = (int) $_POST["article_id"];
            $db = Database::getInstance()->getPdo();
            
            $stmt = $db->prepare("DELETE FROM news_articles WHERE id = ?");
            $stmt->execute([$articleId]);
            
            addFlashMessage("Article deleted successfully", "success");
            error_log("delete_article end");
            redirect(buildNewsRedirectUrl());
        }
        
        if (isset($_POST["bulk_delete"])) {
            error_log("bulk_delete start");
            $deleteType = $_POST["delete_type"];
            $db = Database::getInstance()->getPdo();
            
            $sql = "DELETE FROM news_articles";
            $params = [];
            
            switch ($deleteType) {
                case "all":
                    // Delete all articles
                    break;
                case "read":
                    $sql .= " WHERE is_read = 1";
                    break;
                case "unread":
                    $sql .= " WHERE is_read = 0";
                    break;
                case "not_favorites":
                    $sql .= " WHERE is_favorite = 0";
                    break;
                default:
                    throw new Exception("Invalid delete type");
            }
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $deletedCount = $stmt->rowCount();
            
            addFlashMessage("Deleted {$deletedCount} articles successfully", "success");
            error_log("bulk_delete end");
            redirect(buildNewsRedirectUrl());
        }

    } catch (Exception $e) {
        addFlashMessage("Error: " . $e->getMessage(), "error");
    }
}

// Get articles with filters
$filter = $_GET["filter"] ?? "all";
$page = max(1, (int) ($_GET["page"] ?? 1));
$perPage = 20;
$offset = ($page - 1) * $perPage;

try {
    $db = Database::getInstance()->getPdo();
    
    // Build WHERE clause based on filter
    $whereClause = "WHERE 1=1";
    $params = [];
    
    switch ($filter) {
        case "unread":
            $whereClause .= " AND is_read = 0";
            break;
        case "favorites":
            $whereClause .= " AND is_favorite = 1";
            break;
        case "read":
            $whereClause .= " AND is_read = 1";
            break;
    }
    
    // Get total count
    $countStmt = $db->prepare("SELECT COUNT(*) FROM news_articles $whereClause");
    $countStmt->execute($params);
    $totalArticles = $countStmt->fetchColumn();
    
    // Get articles
    $stmt = $db->prepare("
        SELECT id, site_name, title, url, thumbnail_url, content, scraped_at, is_read, is_favorite
        FROM news_articles 
        $whereClause 
        ORDER BY scraped_at DESC 
        LIMIT $perPage OFFSET $offset
    ");
    $stmt->execute($params);
    $articles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get site statistics
    $statsStmt = $db->prepare("
        SELECT site_name, COUNT(*) as count 
        FROM news_articles 
        GROUP BY site_name 
        ORDER BY count DESC
    ");
    $statsStmt->execute();
    $siteStats = $statsStmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("Error loading news articles: " . $e->getMessage());
    $articles = [];
    $totalArticles = 0;
    $siteStats = [];
    addFlashMessage("Error loading articles.", "error");
}

$totalPages = ceil($totalArticles / $perPage);

$pageTitle = "News Management";
$activeNavItem = "news";
$additionalCSS = '
<style>
    .news-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }
    
    /* Flash message number highlighting */
    .alert {
        font-weight: 500;
    }
    
    .alert-success {
        position: relative;
    }
    
    .alert-success::after {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: #28a745;
        border-radius: 2px;
    }
    
    .news-filters {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .filter-link {
        padding: 0.5rem 1rem;
        text-decoration: none;
        border-radius: 4px;
        transition: all 0.3s;
    }
    
    .filter-link.active {
        background: #007bff;
        color: white;
    }
    
    .filter-link:not(.active) {
        background: #e9ecef;
        color: #495057;
    }
    
    .filter-link:hover {
        background: #0056b3;
        color: white;
    }
    
    .news-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #007bff;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .articles-grid {
        display: grid;
        gap: 1.5rem;
    }
    
    .article-card {
        background: white;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        overflow: hidden;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .article-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .article-card.read {
        opacity: 0.7;
    }
    
    .article-content {
        display: flex;
        gap: 1rem;
        padding: 1.5rem;
    }
    
    .article-thumbnail {
        flex-shrink: 0;
        width: 120px;
        height: 80px;
        background: #f8f9fa;
        border-radius: 4px;
        overflow: hidden;
    }
    
    .article-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .article-info {
        flex: 1;
        min-width: 0;
    }
    
    .article-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }
    
    .article-title a {
        color: #212529;
        text-decoration: none;
    }
    
    .article-title a:hover {
        color: #007bff;
    }
    
    .article-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }
    
    .article-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border: 1px solid #dee2e6;
        background: white;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
    }
    
    .btn-sm:hover {
        background: #f8f9fa;
    }
    
    .btn-favorite {
        color: #ffc107;
    }
    
    .btn-favorite.active {
        background: #ffc107;
        color: white;
        border-color: #ffc107;
    }
    
    .btn-create-post {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }
    
    .btn-create-post:hover {
        background: #0056b3;
        border-color: #0056b3;
    }
    
    .btn-create-post:disabled {
        background: #6c757d;
        border-color: #6c757d;
        cursor: not-allowed;
    }
    
    .site-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        background: #e9ecef;
        color: #495057;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 2rem;
    }
    
    .pagination a {
        padding: 0.5rem 1rem;
        text-decoration: none;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        color: #007bff;
    }
    
    .pagination a:hover {
        background: #f8f9fa;
    }
    
    .pagination .active {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        background: white;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }
    
    .scrape-button {
        background: #28a745;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 4px;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.2s;
    }
    
    .scrape-button:hover {
        background: #218838;
    }
    
    /* Progress Bar Styles */
    .progress-container {
        display: none;
        margin: 1rem 0;
        background: white;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .progress-title {
        font-weight: 600;
        color: #333;
        margin: 0;
    }
    
    .progress-percentage {
        font-weight: 500;
        color: #007bff;
    }
    
    .progress-bar-container {
        width: 100%;
        height: 20px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 1rem;
    }
    
    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #007bff, #28a745);
        border-radius: 10px;
        transition: width 0.3s ease;
        width: 0%;
    }
    
    .progress-message {
        color: #6c757d;
        font-size: 0.9rem;
        margin: 0;
    }
    
    .progress-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .status-icon {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 2px solid #007bff;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
    }
    
    .status-icon.completed {
        border: none;
        background: #28a745;
        animation: none;
        position: relative;
    }
    
    .status-icon.completed::after {
        content: "\\2713";
        color: white;
        font-size: 10px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .scrape-button:disabled {
        background: #6c757d;
        cursor: not-allowed;
    }
    
    /* Tab Styles */
    .news-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .tabs {
        border-bottom: 2px solid #e1e5e9;
        margin-bottom: 30px;
    }

    .tab-nav {
        display: flex;
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .tab-nav li {
        margin-right: 10px;
    }

    .tab-nav a {
        display: block;
        padding: 12px 24px;
        text-decoration: none;
        color: #666;
        border-bottom: 3px solid transparent;
        transition: all 0.3s ease;
    }

    .tab-nav a:hover {
        color: #007cba;
        background-color: #f8f9fa;
    }

    .tab-nav a.active {
        color: #007cba;
        border-bottom-color: #007cba;
        font-weight: 600;
    }

    /* Sources Management Styles */
    .sources-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .sources-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .source-card {
        background: white;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .source-card h3 {
        margin: 0 0 10px 0;
        color: #333;
    }

    .source-url {
        color: #666;
        font-size: 0.9em;
        margin-bottom: 15px;
        word-break: break-all;
    }

    .source-selectors {
        font-size: 0.85em;
        color: #777;
        margin-bottom: 15px;
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
    }

    .source-selectors code {
        background: #e9ecef;
        padding: 2px 4px;
        border-radius: 2px;
        font-size: 0.9em;
    }

    .source-meta {
        font-size: 0.8em;
        color: #999;
        margin-bottom: 15px;
    }

    .source-actions {
        display: flex;
        gap: 10px;
    }

    .btn-edit, .btn-delete {
        font-size: 0.85em;
        padding: 6px 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }

    .btn-edit {
        background-color: #007cba;
        color: white;
    }

    .btn-edit:hover {
        background-color: #005a87;
    }

    .btn-delete {
        background-color: #dc3545;
        color: white;
    }

    .btn-delete:hover {
        background-color: #c82333;
    }

    /* Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        display: none;
        z-index: 1000;
    }

    .modal {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 8px;
        padding: 30px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        z-index: 1001;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    .modal h2 {
        margin-top: 0;
        margin-bottom: 20px;
        color: #333;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #333;
    }

    .form-group input, .form-group textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        box-sizing: border-box;
    }

    .form-group input:focus {
        outline: none;
        border-color: #007cba;
        box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.1);
    }

    .form-group small {
        display: block;
        margin-top: 5px;
        color: #666;
        font-size: 0.85em;
    }

    .modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 30px;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
    }

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #666;
    }
    
    /* Article content styles */
    .article-content-preview {
        margin: 0.75rem 0;
    }
    
    .content-preview {
        margin: 0;
        color: #495057;
        line-height: 1.5;
    }
    
    .content-truncated {
        color: #6c757d;
    }
    
    .btn-expand, .btn-collapse {
        background: #007bff;
        color: white;
        border: none;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        font-size: 14px;
        font-weight: bold;
        cursor: pointer;
        margin-left: 0.5rem;
        vertical-align: middle;
        transition: background-color 0.2s;
    }
    
    .btn-expand:hover, .btn-collapse:hover {
        background: #0056b3;
    }
    
    .content-full {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 4px;
        margin-top: 0.5rem;
        border-left: 3px solid #007bff;
    }
    
    .content-full p {
        margin: 0;
        line-height: 1.6;
        color: #495057;
    }
</style>';

include "header.php";
?>

<div class="news-container">
    <!-- Tab Navigation -->
    <div class="tabs">
        <ul class="tab-nav">
            <li><a href="news.php?action=list" class="<?php echo $action === 'list' ? 'active' : ''; ?>">📰 Articles</a></li>
            <li><a href="news.php?action=sources" class="<?php echo $action === 'sources' ? 'active' : ''; ?>">⚙️ Sources</a></li>
        </ul>
    </div>

    <?php if ($action === "sources"): ?>
        <!-- Sources Management -->
        <div class="sources-header">
            <div>
                <h2>News Sources</h2>
                <p>Manage your news scraping sources</p>
            </div>
            
            <button onclick="openModal('createSourceModal')" class="btn btn-primary">
                ➕ Add New Source
            </button>
        </div>

        <?php
        $sources = loadSources();
        if (empty($sources)):
        ?>
            <div class="empty-state">
                <p>No news sources configured. Add your first source to get started.</p>
            </div>
        <?php else: ?>
            <div class="sources-grid">
                <?php foreach ($sources as $source): ?>
                    <div class="source-card">
                        <h3><?php echo Security::escape($source['name']); ?></h3>
                        <div class="source-url"><?php echo Security::escape($source['url']); ?></div>
                        
                        <div class="source-selectors">
                            <strong>Selectors:</strong><br>
                            Articles: <code><?php echo Security::escape($source['selectors']['ARTICLES'] ?? 'N/A'); ?></code><br>
                            Title: <code><?php echo Security::escape($source['selectors']['TITLE'] ?? 'N/A'); ?></code><br>
                            Thumbnail: <code><?php echo Security::escape($source['selectors']['THUMBNAIL'] ?? 'N/A'); ?></code>
                        </div>
                        
                        <div class="source-meta">
                            <small>Modified: <?php echo date('M j, Y H:i', $source['last_modified']); ?></small>
                        </div>
                        
                        <div class="source-actions">
                            <button onclick="editSource(<?php echo htmlspecialchars(json_encode($source), ENT_QUOTES); ?>)" 
                                    class="btn-sm btn-edit">
                                ✏️ Edit
                            </button>
                            
                            <form method="POST" style="display: inline;" 
                                  onsubmit="return confirm('Are you sure you want to delete this source?');">
                                <?php echo csrfTokenField(); ?>
                                <input type="hidden" name="filename" value="<?php echo Security::escape($source['filename']); ?>">
                                <button type="submit" name="delete_source" class="btn-sm btn-delete">
                                    🗑️ Delete
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Create Source Modal -->
        <div id="createSourceModal" class="modal-overlay">
            <div class="modal">
                <h2>Add New Source</h2>
                <form method="POST">
                    <?php echo csrfTokenField(); ?>
                    
                    <div class="form-group">
                        <label for="source_name">Source Name *</label>
                        <input type="text" id="source_name" name="source_name" required 
                               placeholder="e.g., TechCrunch, Reuters">
                        <small>A friendly name for this news source</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="source_url">Source URL *</label>
                        <input type="url" id="source_url" name="source_url" required 
                               placeholder="https://example.com/news">
                        <small>The URL to scrape articles from</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="articles_selector">Articles Selector *</label>
                        <input type="text" id="articles_selector" name="articles_selector" required 
                               placeholder="div.article, .news-item, article">
                        <small>CSS selector for article containers</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="title_selector">Title Selector</label>
                        <input type="text" id="title_selector" name="title_selector" 
                               placeholder="h2 a, .title, h3.headline">
                        <small>CSS selector for article titles</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="thumbnail_selector">Thumbnail Selector</label>
                        <input type="text" id="thumbnail_selector" name="thumbnail_selector" 
                               placeholder="img, .featured-image img">
                        <small>CSS selector for article images (defaults to 'img')</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="content_selector">Content Selector</label>
                        <input type="text" id="content_selector" name="content_selector" 
                               placeholder=".entry-content, .article-body, .content">
                        <small>CSS selector for article content on individual article pages</small>
                    </div>
                    
                    <div class="modal-actions">
                        <button type="button" onclick="closeModal('createSourceModal')" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" name="create_source" class="btn btn-primary">
                            Create Source
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Edit Source Modal -->
        <div id="editSourceModal" class="modal-overlay">
            <div class="modal">
                <h2>Edit Source</h2>
                <form method="POST">
                    <?php echo csrfTokenField(); ?>
                    <input type="hidden" id="edit_filename" name="filename">
                    
                    <div class="form-group">
                        <label for="edit_source_name">Source Name *</label>
                        <input type="text" id="edit_source_name" name="source_name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_source_url">Source URL *</label>
                        <input type="url" id="edit_source_url" name="source_url" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_articles_selector">Articles Selector *</label>
                        <input type="text" id="edit_articles_selector" name="articles_selector" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_title_selector">Title Selector</label>
                        <input type="text" id="edit_title_selector" name="title_selector">
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_thumbnail_selector">Thumbnail Selector</label>
                        <input type="text" id="edit_thumbnail_selector" name="thumbnail_selector">
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_content_selector">Content Selector</label>
                        <input type="text" id="edit_content_selector" name="content_selector">
                        <small>CSS selector for article content on individual article pages</small>
                    </div>
                    
                    <div class="modal-actions">
                        <button type="button" onclick="closeModal('editSourceModal')" class="btn-secondary">
                            Cancel
                        </button>
                        <button type="submit" name="update_source" class="btn btn-primary">
                            Update Source
                        </button>
                    </div>
                </form>
            </div>
        </div>

    <?php else: ?>
        <!-- Articles List (existing content) -->
        <div class="news-header">
            <div>
                <h2>News Articles</h2>
                <p>Browse and manage scraped news articles</p>
            </div>
            
            <button type="button" onclick="startScraping()" class="scrape-button" id="scrapeButton">
                🔄 Scrape Now
            </button>
            
            <!-- Progress Bar Container -->
            <div id="progressContainer" class="progress-container">
                <div class="progress-header">
                    <h4 class="progress-title">Scraping in Progress</h4>
                    <span class="progress-percentage" id="progressPercentage">0%</span>
                </div>
                <div class="progress-bar-container">
                    <div class="progress-bar" id="progressBar"></div>
                </div>
                <div class="progress-status">
                    <div class="status-icon" id="statusIcon"></div>
                    <p class="progress-message" id="progressMessage">Initializing...</p>
                </div>
            </div>
            
            <div class="bulk-actions-dropdown" style="display: inline-block; margin-left: 1rem;">
                <button type="button" class="bulk-delete-btn" onclick="toggleBulkDropdown()">
                    🗑️ Delete Options ▼
                </button>
                <div class="bulk-dropdown-content" id="bulkDropdown" style="display: none;">
                    <form method="POST" onsubmit="return confirmBulkDelete(this)">
                        <?php echo csrfTokenField(); ?>
                        <input type="hidden" name="delete_type" id="deleteType">
                        <button type="submit" name="bulk_delete" onclick="setBulkDeleteType('all')">Delete All</button>
                        <button type="submit" name="bulk_delete" onclick="setBulkDeleteType('read')">Delete All Read</button>
                        <button type="submit" name="bulk_delete" onclick="setBulkDeleteType('unread')">Delete All Unread</button>
                        <button type="submit" name="bulk_delete" onclick="setBulkDeleteType('not_favorites')">Delete All Not Favorites</button>
                    </form>
                </div>
            </div>
        </div>

<!-- Statistics -->
<div class="news-stats">
    <div class="stat-card">
        <div class="stat-number"><?php echo number_format($totalArticles); ?></div>
        <div class="stat-label">Total Articles</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number"><?php echo count($siteStats); ?></div>
        <div class="stat-label">News Sources</div>
    </div>
    
    <?php
    $unreadCount = 0;
    $favoriteCount = 0;
    
    try {
        $unreadStmt = $db->prepare("SELECT COUNT(*) FROM news_articles WHERE is_read = 0");
        $unreadStmt->execute();
        $unreadCount = $unreadStmt->fetchColumn();
        
        $favoriteStmt = $db->prepare("SELECT COUNT(*) FROM news_articles WHERE is_favorite = 1");
        $favoriteStmt->execute();
        $favoriteCount = $favoriteStmt->fetchColumn();
    } catch (Exception $e) {
        // Ignore errors for stats
    }
    ?>
    
    <div class="stat-card">
        <div class="stat-number"><?php echo number_format($unreadCount); ?></div>
        <div class="stat-label">Unread Articles</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number"><?php echo number_format($favoriteCount); ?></div>
        <div class="stat-label">Favorites</div>
    </div>
</div>

<!-- Filters -->
<div class="news-filters">
    <a href="news.php?filter=all" class="filter-link <?php echo $filter === 'all' ? 'active' : ''; ?>">
        All Articles
    </a>
    <a href="news.php?filter=unread" class="filter-link <?php echo $filter === 'unread' ? 'active' : ''; ?>">
        Unread (<?php echo $unreadCount; ?>)
    </a>
    <a href="news.php?filter=favorites" class="filter-link <?php echo $filter === 'favorites' ? 'active' : ''; ?>">
        Favorites (<?php echo $favoriteCount; ?>)
    </a>
    <a href="news.php?filter=read" class="filter-link <?php echo $filter === 'read' ? 'active' : ''; ?>">
        Read
    </a>
</div>

<!-- Articles List -->
<?php if (empty($articles)): ?>
    <div class="empty-state">
        <h3>No Articles Found</h3>
        <p>No news articles have been scraped yet. Click "Scrape Now" to fetch the latest articles.</p>
    </div>
<?php else: ?>
    <div class="articles-grid">
        <?php foreach ($articles as $article): ?>
            <article class="article-card <?php echo $article['is_read'] ? 'read' : ''; ?>">
                <div class="article-content">
                    <?php if (!empty($article['thumbnail_url'])): ?>
                        <div class="article-thumbnail">
                            <img src="<?php echo Security::escape($article['thumbnail_url']); ?>" 
                                 alt="Article thumbnail"
                                 loading="lazy"
                                 onerror="this.parentElement.style.display='none'">
                        </div>
                    <?php endif; ?>
                    
                    <div class="article-info">
                        <h3 class="article-title">
                            <a href="<?php echo Security::escape($article['url']); ?>" 
                               target="_blank" 
                               rel="noopener noreferrer">
                                <?php echo Security::escape($article['title']); ?>
                            </a>
                        </h3>
                        
                        <?php if (!empty($article['content'])): ?>
                            <div class="article-content-preview">
                                <?php 
                                $content = trim($article['content']);
                                $shortContent = mb_substr($content, 0, 200);
                                $hasMore = mb_strlen($content) > 200;
                                ?>
                                <p class="content-preview">
                                    <?php echo Security::escape($shortContent); ?>
                                    <?php if ($hasMore): ?>
                                        <span class="content-truncated">...</span>
                                        <button type="button" class="btn-expand" onclick="toggleContent(<?php echo $article['id']; ?>)">+</button>
                                    <?php endif; ?>
                                </p>
                                <?php if ($hasMore): ?>
                                    <div class="content-full" id="content-full-<?php echo $article['id']; ?>" style="display: none;">
                                        <p><?php echo nl2br(Security::escape($content)); ?></p>
                                        <button type="button" class="btn-collapse" onclick="toggleContent(<?php echo $article['id']; ?>)">-</button>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="article-meta">
                            <span class="site-badge"><?php echo Security::escape($article['site_name']); ?></span>
                            <span><?php echo formatDate($article['scraped_at']); ?></span>
                            <?php if ($article['is_read']): ?>
                                <span style="color: #28a745;">✓ Read</span>
                            <?php endif; ?>
                            <?php if ($article['is_favorite']): ?>
                                <span style="color: #ffc107;">★ Favorite</span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="article-actions">
                            <?php if (!$article['is_read']): ?>
                                <form method="POST" style="display: inline;">
                                    <?php echo csrfTokenField(); ?>
                                    <input type="hidden" name="article_id" value="<?php echo $article['id']; ?>">
                                    <button type="submit" name="mark_read" class="btn-sm">
                                        Mark as Read
                                    </button>
                                </form>
                            <?php endif; ?>
                            
                            <form method="POST" style="display: inline;">
                                <?php echo csrfTokenField(); ?>
                                <input type="hidden" name="article_id" value="<?php echo $article['id']; ?>">
                                <button type="submit" name="toggle_favorite" 
                                        class="btn-sm btn-favorite <?php echo $article['is_favorite'] ? 'active' : ''; ?>">
                                    <?php echo $article['is_favorite'] ? '★' : '☆'; ?>
                                </button>
                            </form>
                            
                            <button type="button" 
                                    class="btn-sm btn-create-post" 
                                    onclick="createPostFromArticle(<?php echo $article['id']; ?>)"
                                    title="Create Post from Article">
                                📝 Create Post
                            </button>
                            
                            <form method="POST" style="display: inline;">
                                <?php echo csrfTokenField(); ?>
                                <input type="hidden" name="article_id" value="<?php echo $article['id']; ?>">
                                <button type="submit" name="delete_article" class="btn-sm btn-delete" 
                                        onclick="return confirm('Are you sure you want to delete this article?')">
                                    🗑️
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </article>
        <?php endforeach; ?>
    </div>
    
    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
                <a href="news.php?filter=<?php echo $filter; ?>&page=<?php echo $page - 1; ?>">« Previous</a>
            <?php endif; ?>
            
            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                <a href="news.php?filter=<?php echo $filter; ?>&page=<?php echo $i; ?>" 
                   class="<?php echo $i === $page ? 'active' : ''; ?>">
                    <?php echo $i; ?>
                </a>
            <?php endfor; ?>
            
            <?php if ($page < $totalPages): ?>
                <a href="news.php?filter=<?php echo $filter; ?>&page=<?php echo $page + 1; ?>">Next »</a>
            <?php endif; ?>
        </div>
    <?php endif; ?>
<?php endif; ?>
<?php endif; ?>

<script>
let progressInterval = null;

function startScraping() {
    console.log('startScraping start');
    
    const button = document.getElementById('scrapeButton');
    const progressContainer = document.getElementById('progressContainer');
    
    // Disable button and show progress
    button.disabled = true;
    button.textContent = '⏳ Scraping...';
    progressContainer.style.display = 'block';
    
    // Reset progress elements
    updateProgress(0, 'Initializing scraper...');
    
    // Start scraping
    fetch('scraper-progress.php?action=start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'csrf_token=<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Start polling for progress
            pollProgress();
        } else {
            showError('Failed to start scraper: ' + data.error);
            resetScrapeButton();
        }
    })
    .catch(error => {
        console.error('Error starting scraper:', error);
        showError('Failed to start scraper');
        resetScrapeButton();
    });
    
    console.log('startScraping end');
}

function pollProgress() {
    console.log('pollProgress start');
    
    progressInterval = setInterval(() => {
        fetch('scraper-progress.php?action=progress', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'csrf_token=<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.progress) {
                updateProgress(data.progress.percentage, data.progress.message);
                
                // Check for completion
                if (data.progress.percentage >= 100 || !data.isRunning || 
                    (data.progress.message && data.progress.message.includes('Completed'))) {
                    // Scraping completed
                    clearInterval(progressInterval);
                    progressInterval = null;
                    
                    // Ensure we show the completion message
                    const finalMessage = data.progress.message || '✅ Scraping completed successfully!';
                    setTimeout(() => {
                        completeScraping(finalMessage);
                    }, 500); // Small delay to ensure final progress update is visible
                }
            } else if (!data.success) {
                console.error('Progress polling error:', data.error);
                // Don't stop polling on single error, but limit retries
            }
        })
        .catch(error => {
            console.error('Error polling progress:', error);
            // Continue polling in case of temporary network issues
        });
    }, 1500); // Poll every 1.5 seconds for more responsive updates
    
    console.log('pollProgress end');
}

function updateProgress(percentage, message) {
    console.log('updateProgress start', percentage, message);
    
    const progressBar = document.getElementById('progressBar');
    const progressPercentage = document.getElementById('progressPercentage');
    const progressMessage = document.getElementById('progressMessage');
    const statusIcon = document.getElementById('statusIcon');
    
    progressBar.style.width = percentage + '%';
    progressPercentage.textContent = percentage + '%';
    progressMessage.textContent = message;
    
    if (percentage >= 100) {
        statusIcon.classList.add('completed');
    }
    
    console.log('updateProgress end');
}

function completeScraping(message) {
    console.log('completeScraping start');
    
    const button = document.getElementById('scrapeButton');
    
    // Update button to show completion
    button.textContent = '✅ Completed!';
    button.style.background = '#28a745';
    
    // Show success message
    showSuccess(message);
    
    // Reset after 3 seconds and reload page
    setTimeout(() => {
        window.location.reload();
    }, 3000);
    
    console.log('completeScraping end');
}

function resetScrapeButton() {
    console.log('resetScrapeButton start');
    
    const button = document.getElementById('scrapeButton');
    const progressContainer = document.getElementById('progressContainer');
    
    button.disabled = false;
    button.textContent = '🔄 Scrape Now';
    button.style.background = '#28a745';
    progressContainer.style.display = 'none';
    
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }
    
    console.log('resetScrapeButton end');
}

function showSuccess(message) {
    console.log('showSuccess start', message);
    
    // Create and show success notification
    const notification = document.createElement('div');
    notification.className = 'alert alert-success';
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.maxWidth = '500px';
    notification.style.padding = '15px';
    notification.style.borderRadius = '8px';
    notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    notification.style.fontSize = '14px';
    notification.style.fontWeight = '500';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Keep success message visible longer for completion messages
    const displayTime = message.includes('Completed') ? 8000 : 5000;
    
    setTimeout(() => {
        notification.remove();
    }, displayTime);
    
    console.log('showSuccess end');
}

function showError(message) {
    console.log('showError start');
    // Create and show error notification
    const notification = document.createElement('div');
    notification.className = 'alert alert-danger';
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.maxWidth = '400px';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
    console.log('showError end');
}

function openModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function editSource(source) {
    document.getElementById('edit_filename').value = source.filename;
    document.getElementById('edit_source_name').value = source.name;
    document.getElementById('edit_source_url').value = source.url;
    document.getElementById('edit_articles_selector').value = source.selectors.ARTICLES || '';
    document.getElementById('edit_title_selector').value = source.selectors.TITLE || '';
    document.getElementById('edit_thumbnail_selector').value = source.selectors.THUMBNAIL || '';
    document.getElementById('edit_content_selector').value = source.selectors.CONTENT || '';
    
    openModal('editSourceModal');
}

function toggleContent(articleId) {
    console.log('toggleContent start for article', articleId);
    const previewElement = document.querySelector(`#content-full-${articleId}`).previousElementSibling;
    const fullElement = document.getElementById(`content-full-${articleId}`);
    
    if (fullElement.style.display === 'none') {
        // Show full content
        previewElement.style.display = 'none';
        fullElement.style.display = 'block';
    } else {
        // Show preview
        fullElement.style.display = 'none';
        previewElement.style.display = 'block';
    }
    console.log('toggleContent end');
}

// Bulk delete functionality
function toggleBulkDropdown() {
    console.log('toggleBulkDropdown start');
    const dropdown = document.getElementById('bulkDropdown');
    dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
    console.log('toggleBulkDropdown end');
}

function setBulkDeleteType(type) {
    console.log('setBulkDeleteType start', type);
    document.getElementById('deleteType').value = type;
    console.log('setBulkDeleteType end');
}

function confirmBulkDelete(form) {
    console.log('confirmBulkDelete start');
    const deleteType = form.querySelector('input[name="delete_type"]').value;
    let message = '';
    
    switch(deleteType) {
        case 'all':
            message = 'Are you sure you want to delete ALL articles? This action cannot be undone.';
            break;
        case 'read':
            message = 'Are you sure you want to delete all READ articles? This action cannot be undone.';
            break;
        case 'unread':
            message = 'Are you sure you want to delete all UNREAD articles? This action cannot be undone.';
            break;
        case 'not_favorites':
            message = 'Are you sure you want to delete all NON-FAVORITE articles? This action cannot be undone.';
            break;
        default:
            message = 'Are you sure you want to delete these articles? This action cannot be undone.';
    }
    
    const confirmed = confirm(message);
    if (confirmed) {
        // Hide dropdown after confirmation
        document.getElementById('bulkDropdown').style.display = 'none';
    }
    console.log('confirmBulkDelete end', confirmed);
    return confirmed;
}

// Close dropdown when clicking outside
document.addEventListener('click', function(event) {
    const dropdown = document.getElementById('bulkDropdown');
    const button = document.querySelector('.bulk-delete-btn');
    
    if (dropdown && !dropdown.contains(event.target) && event.target !== button) {
        dropdown.style.display = 'none';
    }
});

// Close modal when clicking outside
window.onclick = function(event) {
    if (event.target.classList.contains('modal-overlay')) {
        event.target.style.display = 'none';
    }
}

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modals = document.querySelectorAll('.modal-overlay');
        modals.forEach(modal => modal.style.display = 'none');
        
        // Also close bulk dropdown
        const dropdown = document.getElementById('bulkDropdown');
        if (dropdown) {
            dropdown.style.display = 'none';
        }
    }
});

// Create Post from Article function
function createPostFromArticle(articleId) {
    console.log('createPostFromArticle start', articleId);
    
    // Show loading state
    const button = event.target;
    const originalText = button.textContent;
    button.disabled = true;
    button.textContent = '⏳ Processing...';
    
    // Mark article as favorite and get article data
    fetch('/admin/mark-favorite.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            'action': 'mark_favorite',
            'article_id': articleId,
            'csrf_token': '<?php echo $_SESSION['csrf_token']; ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.article) {
            // Redirect to posts.php with prefilled data
            const title = encodeURIComponent(data.article.title);
            const content = encodeURIComponent(data.article.content);
            const url = `/admin/posts.php?action=new&title=${title}&content=${content}`;
            
            console.log('createPostFromArticle end - redirecting to posts');
            window.location.href = url;
        } else {
            console.error('createPostFromArticle error:', data.error);
            alert('Error: ' + (data.error || 'Failed to create post'));
            
            // Reset button
            button.disabled = false;
            button.textContent = originalText;
        }
    })
    .catch(error => {
        console.error('createPostFromArticle fetch error:', error);
        alert('Network error: Failed to create post');
        
        // Reset button
        button.disabled = false;
        button.textContent = originalText;
    });
}
</script>

<?php include "footer.php"; ?>
