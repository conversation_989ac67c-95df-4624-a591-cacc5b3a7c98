<?php
/**
 * Mark Article as Favorite API
 * Handles AJAX requests for marking news articles as favorites
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "../../src/includes/init.php";
}

// Require authentication
$auth->requireAuth();

header('Content-Type: application/json');

function markArticleAsFavorite($article_id) {
    error_log("markArticleAsFavorite start");
    try {
        $db_path = dirname(__DIR__, 2) . '/database/cms.db';
        $conn = new PDO("sqlite:$db_path");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Mark article as favorite AND read (when creating post from article, user has read it)
        $stmt = $conn->prepare("UPDATE news_articles SET is_favorite = 1, is_read = 1 WHERE id = ?");
        $result = $stmt->execute([$article_id]);
        
        if ($stmt->rowCount() > 0) {
            error_log("markArticleAsFavorite end - success");
            return ['success' => true, 'message' => 'Article marked as favorite and read'];
        } else {
            error_log("markArticleAsFavorite end - article not found");
            return ['success' => false, 'error' => 'Article not found'];
        }
        
    } catch (PDOException $e) {
        error_log("markArticleAsFavorite end - database error");
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

function getArticleData($article_id) {
    error_log("getArticleData start");
    try {
        $db_path = dirname(__DIR__, 2) . '/database/cms.db';
        $conn = new PDO("sqlite:$db_path");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $conn->prepare("SELECT id, title, content FROM news_articles WHERE id = ?");
        $stmt->execute([$article_id]);
        $article = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($article) {
            error_log("getArticleData end - success");
            return ['success' => true, 'article' => $article];
        } else {
            error_log("getArticleData end - article not found");
            return ['success' => false, 'error' => 'Article not found'];
        }
        
    } catch (PDOException $e) {
        error_log("getArticleData end - database error");
        return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
    }
}

// Handle the request
try {
    validateCSRF();
    
    $action = $_POST['action'] ?? '';
    $article_id = (int)($_POST['article_id'] ?? 0);
    
    if (!$article_id) {
        echo json_encode(['success' => false, 'error' => 'Invalid article ID']);
        exit;
    }
    
    switch ($action) {
        case 'mark_favorite':
            $result = markArticleAsFavorite($article_id);
            
            if ($result['success']) {
                // Get article data for redirect
                $article_data = getArticleData($article_id);
                if ($article_data['success']) {
                    $result['article'] = $article_data['article'];
                }
            }
            
            echo json_encode($result);
            break;
            
        default:
            echo json_encode(['success' => false, 'error' => 'Invalid action']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
