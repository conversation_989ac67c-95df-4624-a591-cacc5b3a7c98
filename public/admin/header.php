<?php
/**
 * Admin Header Template
 * Reusable header for all admin pages
 * 
 * Usage:
 * $pageTitle = "Your Page Title";
 * $activeNavItem = "posts"; // dashboard, posts, media, profile, api, or empty string
 * $additionalMeta = '<meta name="description" content="...">'; // Optional additional meta tags
 * $additionalCSS = '<style>...</style>'; // Optional additional CSS
 * include "header.php";
 */

// Ensure this is only included from admin pages that have authentication
if (!defined("CMS_INIT")) {
    die("Direct access not allowed");
}

// Get page-specific variables
$pageTitle = $pageTitle ?? "Admin";
$activeNavItem = $activeNavItem ?? "";
$includeCSRF = $includeCSRF ?? true;
$additionalMeta = $additionalMeta ?? "";
$additionalCSS = $additionalCSS ?? "";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php if ($includeCSRF): ?>
    <meta name="csrf-token" content="<?php echo Session::get("csrf_token"); ?>">
    <?php endif; ?>
    <?php echo $additionalMeta; ?>
    <title><?php echo Security::escape($pageTitle); ?> - Admin</title>
    <link rel="stylesheet" href="/assets/style.css">
    <?php echo $additionalCSS; ?>
</head>
<body class="admin-page">
    <header class="admin-header">
        <div class="container">
            <h1 class="admin-title">
                <a href="index.php">Admin Dashboard</a>
            </h1>

            <nav class="admin-nav">
                <ul>
                    <li><a href="index.php"<?php echo $activeNavItem === 'dashboard' ? ' class="active"' : ''; ?>>Dashboard</a></li>
                    <li><a href="posts.php"<?php echo $activeNavItem === 'posts' ? ' class="active"' : ''; ?>>Posts</a></li>
                    <li><a href="media.php"<?php echo $activeNavItem === 'media' ? ' class="active"' : ''; ?>>Media</a></li>
                    <li><a href="news.php"<?php echo $activeNavItem === 'news' ? ' class="active"' : ''; ?>>News</a></li>
                    <li><a href="profile.php"<?php echo $activeNavItem === 'profile' ? ' class="active"' : ''; ?>>Profile</a></li>
                    <li><a href="api.php"<?php echo $activeNavItem === 'api' ? ' class="active"' : ''; ?>>API</a></li>
                    <li><a href="../index.php" target="_blank">View Site</a></li>
                    <li><a href="logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="admin-main">
        <div class="container">
            <?php echo displayFlashMessages(); ?>
