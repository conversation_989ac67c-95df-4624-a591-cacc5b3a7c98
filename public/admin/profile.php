<?php
/**
 * Admin Profile Management
 * Allows admin to change password and view account information
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "../../src/includes/init.php";
}

// Require authentication
$auth->requireAuth();

$currentUser = $auth->getCurrentUser();
$error = "";
$success = "";

// Handle API token regeneration
if (
    $_SERVER["REQUEST_METHOD"] === "POST" &&
    isset($_POST["regenerate_token"])
) {
    try {
        validateCSRF();

        $newToken = $auth->generateApiToken($currentUser["id"]);

        // Log activity
        logActivity(
            "api_token_regenerated",
            "User regenerated API token",
            $currentUser["id"]
        );

        $success = "API token regenerated successfully!";
        // Refresh user data to show new token
        $currentUser = $auth->getCurrentUser();
    } catch (Exception $e) {
        $error = $e->getMessage();
        logActivity(
            "api_token_regeneration_failed",
            "API token regeneration failed: " . $error,
            $currentUser["id"]
        );
    }
}

// Handle password change form submission
elseif ($_SERVER["REQUEST_METHOD"] === "POST") {
    try {
        validateCSRF();

        $currentPassword = $_POST["current_password"] ?? "";
        $newPassword = $_POST["new_password"] ?? "";
        $confirmPassword = $_POST["confirm_password"] ?? "";

        // Validate input
        if (
            empty($currentPassword) ||
            empty($newPassword) ||
            empty($confirmPassword)
        ) {
            throw new Exception("All password fields are required.");
        }

        if ($newPassword !== $confirmPassword) {
            throw new Exception("New password and confirmation do not match.");
        }

        if ($newPassword === $currentPassword) {
            throw new Exception(
                "New password must be different from current password."
            );
        }

        // Change password
        $auth->changePassword(
            $currentUser["id"],
            $currentPassword,
            $newPassword
        );

        // Log activity
        logActivity(
            "password_changed",
            "User changed password",
            $currentUser["id"]
        );

        $success = "Password changed successfully!";
    } catch (Exception $e) {
        $error = $e->getMessage();
        logActivity(
            "password_change_failed",
            "Password change failed: " . $error,
            $currentUser["id"]
        );
    }
}

$pageTitle = "Profile";
$activeNavItem = "profile";
include "header.php";
?>

            <div class="page-header">
                <h2>Profile Settings</h2>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo Security::escape($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <?php echo Security::escape($success); ?>
                </div>
            <?php endif; ?>

            <div class="profile-sections">
                <!-- Account Information -->
                <div class="profile-section">
                    <h3>Account Information</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Username:</label>
                            <span><?php echo Security::escape(
                                $currentUser["username"]
                            ); ?></span>
                        </div>
                        <div class="info-item">
                            <label>Email:</label>
                            <span><?php echo $currentUser["email"]
                                ? Security::escape($currentUser["email"])
                                : "Not set"; ?></span>
                        </div>
                        <div class="info-item">
                            <label>Account Created:</label>
                            <span><?php echo formatDate(
                                $currentUser["created_at"]
                            ); ?></span>
                        </div>
                        <div class="info-item">
                            <label>Last Login:</label>
                            <span><?php echo $currentUser["last_login"]
                                ? formatDate($currentUser["last_login"])
                                : "First time"; ?></span>
                        </div>
                    </div>
                </div>

                <!-- API Token Management -->
                <div class="profile-section">
                    <h3>API Token</h3>
                    <div class="api-token-section">
                        <div class="form-group">
                            <label for="api_token">Your API Token</label>
                            <div class="token-display">
                                <input type="text"
                                       id="api_token"
                                       name="api_token"
                                       value="<?php echo Security::escape(
                                           $currentUser["api_token"] ?? ""
                                       ); ?>"
                                       class="form-control"
                                       readonly>
                                <button type="button" id="copy_token" class="button button-secondary">
                                    Copy Token
                                </button>
                            </div>
                            <div class="form-help">
                                Use this token to authenticate API requests. Keep it secure and don't share it.
                            </div>
                        </div>

                        <form method="POST" action="profile.php" class="token-form">
                            <?php echo csrfTokenField(); ?>
                            <input type="hidden" name="regenerate_token" value="1">
                            <button type="submit" class="button button-warning"
                                    onclick="return confirm('Are you sure you want to regenerate your API token? The old token will stop working immediately.')">
                                Regenerate Token
                            </button>
                        </form>

                        <div class="api-info">
                            <h4>API Usage:</h4>
                            <p>Include the token in your requests as a Bearer token:</p>
                            <code>Authorization: Bearer <?php echo Security::escape(
                                $currentUser["api_token"] ?? "YOUR_TOKEN"
                            ); ?></code>
                        </div>
                    </div>
                </div>

                <!-- Change Password -->
                <div class="profile-section">
                    <h3>Change Password</h3>
                    <form method="POST" action="profile.php" class="password-form">
                        <?php echo csrfTokenField(); ?>

                        <div class="form-group">
                            <label for="current_password">Current Password *</label>
                            <input type="password"
                                   id="current_password"
                                   name="current_password"
                                   required
                                   autocomplete="current-password"
                                   class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="new_password">New Password *</label>
                            <input type="password"
                                   id="new_password"
                                   name="new_password"
                                   required
                                   autocomplete="new-password"
                                   class="form-control">
                            <div class="form-help">
                                Password must be at least 8 characters long and contain both letters and numbers.
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">Confirm New Password *</label>
                            <input type="password"
                                   id="confirm_password"
                                   name="confirm_password"
                                   required
                                   autocomplete="new-password"
                                   class="form-control">
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="button button-primary">
                                Change Password
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Security Information -->
                <div class="profile-section">
                    <h3>Security Information</h3>
                    <div class="security-info">
                        <div class="info-item">
                            <label>Current IP Address:</label>
                            <span><?php echo Security::getClientIP(); ?></span>
                        </div>
                        <div class="info-item">
                            <label>Browser:</label>
                            <span><?php echo Security::escape(
                                $_SERVER["HTTP_USER_AGENT"] ?? "Unknown"
                            ); ?></span>
                        </div>
                        <div class="security-tips">
                            <h4>Security Tips:</h4>
                            <ul>
                                <li>Use a strong, unique password</li>
                                <li>Log out when finished</li>
                                <li>Don't share your login credentials</li>
                                <li>Access admin panel only from trusted devices</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Password strength validation
        document.getElementById('new_password').addEventListener('input', function() {
            const password = this.value;
            const isValid = password.length >= 8 &&
                           /[A-Za-z]/.test(password) &&
                           /[0-9]/.test(password);

            this.style.borderColor = isValid ? '#28a745' : '#dc3545';
        });

        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            const matches = newPassword === confirmPassword;

            this.style.borderColor = matches ? '#28a745' : '#dc3545';
        });

        // Form validation
        document.querySelector('.password-form').addEventListener('submit', function(e) {
            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (!currentPassword || !newPassword || !confirmPassword) {
                e.preventDefault();
                alert('All password fields are required.');
                return false;
            }

            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('New password and confirmation do not match.');
                return false;
            }

            if (newPassword.length < 8 || !/[A-Za-z]/.test(newPassword) || !/[0-9]/.test(newPassword)) {
                e.preventDefault();
                alert('Password must be at least 8 characters long and contain both letters and numbers.');
                return false;
            }

            if (newPassword === currentPassword) {
                e.preventDefault();
                alert('New password must be different from current password.');
                return false;
            }
        });

        // Copy token functionality
        document.getElementById('copy_token').addEventListener('click', function() {
            const tokenInput = document.getElementById('api_token');
            tokenInput.select();
            tokenInput.setSelectionRange(0, 99999); // For mobile devices

            try {
                document.execCommand('copy');
                this.textContent = 'Copied!';
                this.style.backgroundColor = '#28a745';

                setTimeout(() => {
                    this.textContent = 'Copy Token';
                    this.style.backgroundColor = '';
                }, 2000);
            } catch (err) {
                alert('Failed to copy token. Please select and copy manually.');
            }
        });
    </script>

<?php include "footer.php"; ?>
