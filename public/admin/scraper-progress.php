<?php
/**
 * Scraper Progress API
 * Handles AJAX requests for scraping progress and execution
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "../../src/includes/init.php";
}

// Require authentication
$auth->requireAuth();

header('Content-Type: application/json');

$action = $_GET['action'] ?? $_POST['action'] ?? '';

function writeProgressLog($message, $step = 0, $total = 100) {
    $logFile = dirname(__DIR__, 2) . '/logs/scraper-progress.log';
    $data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'step' => $step,
        'total' => $total,
        'percentage' => $total > 0 ? round(($step / $total) * 100) : 0
    ];
    file_put_contents($logFile, json_encode($data) . "\n", FILE_APPEND | LOCK_EX);
}

function getProgressLog() {
    $logFile = dirname(__DIR__, 2) . '/logs/scraper-progress.log';
    if (!file_exists($logFile)) {
        return null;
    }
    
    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if (empty($lines)) {
        return null;
    }
    
    $lastLine = end($lines);
    $data = json_decode($lastLine, true);
    return $data;
}

function clearProgressLog() {
    $logFile = dirname(__DIR__, 2) . '/logs/scraper-progress.log';
    if (file_exists($logFile)) {
        unlink($logFile);
    }
}

switch ($action) {
    case 'start':
        try {
            validateCSRF();
            
            // Clear previous progress log
            clearProgressLog();
            
            // Initialize progress
            writeProgressLog("Initializing scraper...", 0, 100);
            
            // Start scraper in background using proper background execution
            $workingDir = dirname(__DIR__, 2);
            $command = "cd " . escapeshellarg($workingDir) . " && python3 scraper.py > logs/scraper-output.log 2>&1 &";
            
            // Use shell_exec with nohup for true background execution
            $fullCommand = "nohup bash -c " . escapeshellarg($command) . " > /dev/null 2>&1 &";
            shell_exec($fullCommand);
            
            // Give it a moment to start
            usleep(100000); // 0.1 seconds
            
            writeProgressLog("Scraper started...", 10, 100);
            
            echo json_encode([
                'success' => true,
                'message' => 'Scraper started successfully'
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
        break;
        
    case 'progress':
        try {
            // Validate CSRF for POST requests
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                validateCSRF();
            }
            
            $progress = getProgressLog();
            
            // Check if scraper process is still running
            $isRunning = false;
            $outputFile = dirname(__DIR__, 2) . '/logs/scraper-output.log';
            
            if (file_exists($outputFile)) {
                $output = file_get_contents($outputFile);
                
                // Check for completion first
                if (strpos($output, 'SCRAPER_SUMMARY') !== false && strpos($output, 'scraper run end') !== false) {
                    // Extract final results only if not already at 100%
                    $currentProgress = getProgressLog();
                    if (!$currentProgress || $currentProgress['percentage'] < 100) {
                        if (preg_match('/SCRAPER_SUMMARY: found=(\d+) added=(\d+) skipped=(\d+)/', $output, $matches)) {
                            $found = (int)$matches[1];
                            $added = (int)$matches[2];
                            $skipped = (int)$matches[3];
                            writeProgressLog("✅ Completed! Found: {$found} | Added: {$added} | Skipped: {$skipped}", 100, 100);
                            $progress = getProgressLog();
                        } else {
                            writeProgressLog("✅ Scraping completed!", 100, 100);
                            $progress = getProgressLog();
                        }
                    }
                    $isRunning = false;
                } else {
                    // Update progress based on scraper output patterns only if not completed
                    $currentProgress = getProgressLog();
                    $currentPercentage = $currentProgress ? $currentProgress['percentage'] : 0;
                    
                    // Only update if we haven't reached that stage yet
                    if ($currentPercentage < 15 && strpos($output, 'scraper run start') !== false) {
                        writeProgressLog("Starting scraper process...", 15, 100);
                        $progress = getProgressLog();
                    } else if ($currentPercentage < 25 && strpos($output, 'load_existing_titles start') !== false) {
                        writeProgressLog("Loading existing articles...", 25, 100);
                        $progress = getProgressLog();
                    } else if ($currentPercentage < 35 && strpos($output, 'Starting news scraping process') !== false) {
                        writeProgressLog("Initializing news sources...", 35, 100);
                        $progress = getProgressLog();
                    } else if ($currentPercentage < 50 && strpos($output, 'Scraping') !== false && strpos($output, 'from') !== false) {
                        writeProgressLog("Scraping news sources...", 50, 100);
                        $progress = getProgressLog();
                    } else if ($currentPercentage < 65 && strpos($output, 'Found') !== false && strpos($output, 'article elements') !== false) {
                        writeProgressLog("Processing articles...", 65, 100);
                        $progress = getProgressLog();
                    } else if ($currentPercentage < 75 && strpos($output, 'is_valid_article_url') !== false) {
                        writeProgressLog("Validating article URLs...", 75, 100);
                        $progress = getProgressLog();
                    } else if ($currentPercentage < 85 && strpos($output, 'extract_article_content') !== false) {
                        writeProgressLog("Extracting article content...", 85, 100);
                        $progress = getProgressLog();
                    } else if ($currentPercentage < 95 && strpos($output, 'save_to_database') !== false) {
                        writeProgressLog("Saving articles to database...", 95, 100);
                        $progress = getProgressLog();
                    }
                    
                    // Check if still running
                    if (strpos($output, 'scraper run end') !== false) {
                        $isRunning = false;
                    } else {
                        $isRunning = true;
                    }
                }
            }
            
            echo json_encode([
                'success' => true,
                'progress' => $progress,
                'isRunning' => $isRunning
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
        break;
        
    default:
        echo json_encode([
            'success' => false,
            'error' => 'Invalid action'
        ]);
}
?>
