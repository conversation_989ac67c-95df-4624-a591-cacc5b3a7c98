<?php
/**
 * Session Debug Page
 * Helps monitor session status and troubleshoot timeout issues
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "../../src/includes/init.php";
}

// Require authentication
$auth->requireAuth();

$currentUser = $auth->getCurrentUser();
$sessionInfo = Session::getSessionInfo();
$timeRemaining = Session::getTimeRemaining();
$isAboutToExpire = Session::isAboutToExpire();

// Handle session extension
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["extend_session"])) {
    try {
        validateCSRF();
        Session::extend();
        addFlashMessage("Session extended successfully!", "success");
        redirect("session-debug.php");
    } catch (Exception $e) {
        addFlashMessage(
            "Error extending session: " . $e->getMessage(),
            "error"
        );
    }
}

function formatTime($seconds)
{
    if ($seconds <= 0) {
        return "0 seconds";
    }

    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;

    $parts = [];
    if ($hours > 0) {
        $parts[] = $hours . " hour" . ($hours != 1 ? "s" : "");
    }
    if ($minutes > 0) {
        $parts[] = $minutes . " minute" . ($minutes != 1 ? "s" : "");
    }
    if ($secs > 0 || empty($parts)) {
        $parts[] = $secs . " second" . ($secs != 1 ? "s" : "");
    }

    return implode(", ", $parts);
}

$pageTitle = "Session Debug";
$activeNavItem = ""; // No specific nav item for this debug page
$additionalMeta = '<meta http-equiv="refresh" content="30">'; // Auto-refresh every 30 seconds
$additionalCSS = '
    <style>
        .session-status {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            font-weight: bold;
        }
        .status-active { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status-expired { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .session-info { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .session-info table { width: 100%; border-collapse: collapse; }
        .session-info th, .session-info td { padding: 8px; text-align: left; border-bottom: 1px solid #dee2e6; }
        .session-info th { background: #e9ecef; font-weight: 600; }
        .auto-refresh { font-size: 12px; color: #6c757d; margin: 10px 0; }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; transition: width 0.3s ease; }
        .progress-good { background: #28a745; }
        .progress-warning { background: #ffc107; }
        .progress-danger { background: #dc3545; }
    </style>';
include "header.php";
?>

            <div class="page-header">
                <h2>Session Debug Information</h2>
                <div class="auto-refresh">
                    🔄 Auto-refreshing every 30 seconds
                </div>
            </div>

            <!-- Session Status -->
            <div class="session-status <?php if ($timeRemaining <= 0) {
                echo "status-expired";
            } elseif ($isAboutToExpire) {
                echo "status-warning";
            } else {
                echo "status-active";
            } ?>">
                <?php if ($timeRemaining <= 0): ?>
                    ⚠️ Session Expired
                <?php elseif ($isAboutToExpire): ?>
                    ⚠️ Session expires in <?php echo formatTime(
                        $timeRemaining
                    ); ?>
                <?php else: ?>
                    ✅ Session Active - <?php echo formatTime(
                        $timeRemaining
                    ); ?> remaining
                <?php endif; ?>
            </div>

            <!-- Time Progress Bar -->
            <?php if ($sessionInfo): ?>
                <?php
                $totalTime = $sessionInfo["session_lifetime"];
                $percentage = ($timeRemaining / $totalTime) * 100;
                $progressClass = "progress-good";
                if ($percentage < 25) {
                    $progressClass = "progress-danger";
                } elseif ($percentage < 50) {
                    $progressClass = "progress-warning";
                }
                ?>
                <div class="progress-bar">
                    <div class="progress-fill <?php echo $progressClass; ?>"
                         style="width: <?php echo max(
                             0,
                             $percentage
                         ); ?>%"></div>
                </div>
                <div style="text-align: center; font-size: 14px; color: #6c757d;">
                    <?php echo round(
                        $percentage,
                        1
                    ); ?>% of session time remaining
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="dashboard-section">
                <h3>Quick Actions</h3>
                <div class="quick-actions">
                    <form method="POST" style="display: inline;">
                        <?php echo csrfTokenField(); ?>
                        <input type="hidden" name="extend_session" value="1">
                        <button type="submit" class="button button-primary">
                            🔄 Extend Session
                        </button>
                    </form>
                    <button onclick="location.reload()" class="button">
                        🔃 Refresh Now
                    </button>
                    <button onclick="toggleAutoRefresh()" class="button" id="autoRefreshBtn">
                        ⏸️ Pause Auto-Refresh
                    </button>
                </div>
            </div>

            <!-- Current User Info -->
            <div class="dashboard-section">
                <h3>Current User</h3>
                <div class="session-info">
                    <table>
                        <tr>
                            <th>Username</th>
                            <td><?php echo Security::escape(
                                $currentUser["username"]
                            ); ?></td>
                        </tr>
                        <tr>
                            <th>User ID</th>
                            <td><?php echo $currentUser["id"]; ?></td>
                        </tr>
                        <tr>
                            <th>Last Login</th>
                            <td><?php echo $currentUser["last_login"]
                                ? formatDate($currentUser["last_login"])
                                : "First time"; ?></td>
                        </tr>
                        <tr>
                            <th>Current Time</th>
                            <td><?php echo date("Y-m-d H:i:s T"); ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Session Details -->
            <?php if ($sessionInfo): ?>
                <div class="dashboard-section">
                    <h3>Session Details</h3>
                    <div class="session-info">
                        <table>
                            <tr>
                                <th>Session ID</th>
                                <td><code><?php echo Security::escape(
                                    $sessionInfo["id"]
                                ); ?></code></td>
                            </tr>
                            <tr>
                                <th>Created</th>
                                <td><?php echo $sessionInfo[
                                    "created_at_formatted"
                                ]; ?></td>
                            </tr>
                            <tr>
                                <th>Last Activity</th>
                                <td><?php echo $sessionInfo[
                                    "last_activity_formatted"
                                ]; ?></td>
                            </tr>
                            <tr>
                                <th>Time Since Last Activity</th>
                                <td><?php echo formatTime(
                                    $sessionInfo["seconds_since_activity"]
                                ); ?></td>
                            </tr>
                            <tr>
                                <th>Time Remaining</th>
                                <td><?php echo formatTime(
                                    $sessionInfo["time_remaining"]
                                ); ?></td>
                            </tr>
                            <tr>
                                <th>Session Lifetime</th>
                                <td><?php echo formatTime(
                                    $sessionInfo["session_lifetime"]
                                ); ?></td>
                            </tr>
                            <tr>
                                <th>IP Address</th>
                                <td><?php echo Security::escape(
                                    $sessionInfo["ip_address"]
                                ); ?></td>
                            </tr>
                            <tr>
                                <th>User Agent</th>
                                <td style="word-break: break-all; font-size: 12px;">
                                    <?php echo Security::escape(
                                        $sessionInfo["user_agent"]
                                    ); ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Configuration -->
            <div class="dashboard-section">
                <h3>Session Configuration</h3>
                <div class="session-info">
                    <table>
                        <tr>
                            <th>Session Lifetime</th>
                            <td><?php echo formatTime(
                                Config::SESSION_LIFETIME
                            ); ?></td>
                        </tr>
                        <tr>
                            <th>Warning Time</th>
                            <td><?php echo formatTime(
                                Config::SESSION_WARNING_TIME
                            ); ?></td>
                        </tr>
                        <tr>
                            <th>Regenerate Interval</th>
                            <td><?php echo formatTime(
                                Config::SESSION_REGENERATE_INTERVAL
                            ); ?></td>
                        </tr>
                        <tr>
                            <th>PHP Session Lifetime</th>
                            <td><?php echo ini_get(
                                "session.gc_maxlifetime"
                            ); ?> seconds</td>
                        </tr>
                        <tr>
                            <th>PHP Cookie Lifetime</th>
                            <td><?php echo ini_get(
                                "session.cookie_lifetime"
                            ); ?> seconds</td>
                        </tr>
                        <tr>
                            <th>Session Cookie Secure</th>
                            <td><?php echo ini_get("session.cookie_secure")
                                ? "Yes"
                                : "No"; ?></td>
                        </tr>
                        <tr>
                            <th>Session Cookie HttpOnly</th>
                            <td><?php echo ini_get("session.cookie_httponly")
                                ? "Yes"
                                : "No"; ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- PHP Session Info -->
            <div class="dashboard-section">
                <h3>PHP Session Information</h3>
                <div class="session-info">
                    <table>
                        <tr>
                            <th>Session Name</th>
                            <td><?php echo session_name(); ?></td>
                        </tr>
                        <tr>
                            <th>Session ID</th>
                            <td><code><?php echo session_id(); ?></code></td>
                        </tr>
                        <tr>
                            <th>Session Status</th>
                            <td>
                                <?php
                                $status = session_status();
                                switch ($status) {
                                    case PHP_SESSION_DISABLED:
                                        echo "Disabled";
                                        break;
                                    case PHP_SESSION_NONE:
                                        echo "No session started";
                                        break;
                                    case PHP_SESSION_ACTIVE:
                                        echo "Active";
                                        break;
                                    default:
                                        echo "Unknown (" . $status . ")";
                                }
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Session Variables</th>
                            <td>
                                <details>
                                    <summary>Show session data (<?php echo count(
                                        $_SESSION
                                    ); ?> items)</summary>
                                    <pre style="margin-top: 10px; background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;"><?php
                                    $sessionData = $_SESSION;
                                    // Hide sensitive data
                                    if (isset($sessionData["csrf_token"])) {
                                        $sessionData["csrf_token"] = "[HIDDEN]";
                                    }
                                    echo htmlspecialchars(
                                        print_r($sessionData, true)
                                    );
                                    ?></pre>
                                </details>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Troubleshooting Tips -->
            <div class="dashboard-section">
                <h3>Troubleshooting Tips</h3>
                <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; border-left: 4px solid #2196f3;">
                    <h4>If you're getting logged out frequently:</h4>
                    <ul style="margin: 10px 0 0 20px;">
                        <li>Check if your session lifetime is sufficient (currently <?php echo formatTime(
                            Config::SESSION_LIFETIME
                        ); ?>)</li>
                        <li>Make sure you're not switching between HTTP and HTTPS</li>
                        <li>Check if your browser is blocking cookies</li>
                        <li>Verify your system clock is correct</li>
                        <li>Look for JavaScript errors that might prevent AJAX activity updates</li>
                        <li>Check if multiple tabs are causing session conflicts</li>
                    </ul>
                </div>
            </div>
        </div>
    </main>

    <script>
        let autoRefreshEnabled = true;
        let autoRefreshInterval;

        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            if (autoRefreshEnabled) {
                autoRefreshEnabled = false;
                btn.textContent = '▶️ Resume Auto-Refresh';
                btn.classList.add('button-primary');
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                }
            } else {
                autoRefreshEnabled = true;
                btn.textContent = '⏸️ Pause Auto-Refresh';
                btn.classList.remove('button-primary');
                location.reload(); // Restart the meta refresh
            }
        }

        // Show countdown until next refresh
        let countdown = 30;
        const countdownInterval = setInterval(() => {
            if (!autoRefreshEnabled) {
                clearInterval(countdownInterval);
                return;
            }
            countdown--;
            if (countdown <= 0) {
                countdown = 30;
            }
            document.querySelector('.auto-refresh').innerHTML =
                `🔄 Auto-refreshing in ${countdown} seconds`;
        }, 1000);

        // Warn if session is about to expire
        <?php if ($isAboutToExpire && $timeRemaining > 0): ?>
            if (confirm('Your session will expire in <?php echo formatTime(
                $timeRemaining
            ); ?>. Would you like to extend it?')) {
                // Submit the extend form
                const form = document.querySelector('form[method="POST"]');
                if (form) {
                    form.submit();
                }
            }
        <?php endif; ?>
    </script>

<?php include "footer.php"; ?>
