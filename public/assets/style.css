/* Minimal CMS Stylesheet */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family:
        -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
        Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
}
h2 {
    font-size: 2rem;
}
h3 {
    font-size: 1.5rem;
}
h4 {
    font-size: 1.25rem;
}

p {
    margin-bottom: 1rem;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Header Styles */
.site-header {
    background: #fff;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.site-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-title a {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    text-decoration: none;
}

.main-nav ul {
    list-style: none;
    display: flex;
    gap: 1rem;
}

.main-nav a {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.main-nav a:hover {
    background-color: #f8f9fa;
    text-decoration: none;
}

/* Admin Header */
.admin-header {
    background: #343a40;
    color: white;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.admin-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-title a {
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
}

.admin-nav ul {
    list-style: none;
    display: flex;
    gap: 0;
}

.admin-nav a {
    padding: 0.75rem 1rem;
    color: #adb5bd;
    text-decoration: none;
    transition: all 0.2s;
}

.admin-nav a:hover,
.admin-nav a.active {
    background-color: #495057;
    color: white;
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    margin-bottom: 2rem;
}

/* Buttons */
.button {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.button:hover {
    background-color: #5a6268;
    text-decoration: none;
    color: white;
}

.button-primary {
    background-color: #007bff;
}

.button-primary:hover {
    background-color: #0056b3;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
}

.form-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.form-help {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* Alerts */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-published {
    background-color: #d4edda;
    color: #155724;
}

.status-draft {
    background-color: #fff3cd;
    color: #856404;
}

/* Category Badges */
.category-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
    text-decoration: none;
    display: inline-block;
    transition: opacity 0.2s ease;
}

.category-badge:hover {
    opacity: 0.8;
    text-decoration: none;
}

.category-tech {
    background-color: #e3f2fd;
    color: #1565c0;
}

.category-gaming {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.category-film {
    background-color: #fff3e0;
    color: #ef6c00;
}

.category-serie {
    background-color: #e8f5e8;
    color: #2e7d32;
}

/* Action Links */
.actions {
    white-space: nowrap;
}

.action-link {
    margin-right: 1rem;
    font-size: 0.875rem;
}

.action-link.danger {
    color: #dc3545;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin: 2rem 0;
}

.page-link {
    padding: 0.5rem 0.75rem;
    border: 1px solid #dee2e6;
    background: white;
    color: #007bff;
    text-decoration: none;
    border-radius: 4px;
}

.page-link:hover {
    background-color: #e9ecef;
    text-decoration: none;
}

.page-link.current {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.page-ellipsis {
    padding: 0.5rem 0.75rem;
    color: #6c757d;
}

/* Login Page */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 2rem;
}

.login-form-wrapper {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h1 {
    color: #333;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #6c757d;
    margin-bottom: 0;
}

.login-footer {
    text-align: center;
    margin-top: 1rem;
}

/* Dashboard */
.dashboard-welcome {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    margin-bottom: 1rem;
}

.stat-action a {
    font-size: 0.875rem;
}

/* Dashboard Sections */
.dashboard-section {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dashboard-section h3 {
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.quick-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.section-footer {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
    text-align: center;
}

/* Posts */
.post-preview {
    background: white;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: row;
    min-height: 200px;
}

.post-thumbnail {
    width: 300px;
    flex-shrink: 0;
    overflow: hidden;
    margin: 15px 0 15px 15px;
}

.thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    border-radius: 12px;
}

.thumbnail-image:hover {
    transform: scale(1.05);
}

.post-details {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.post-header {
    margin-bottom: 1rem;
}

.post-title {
    margin-bottom: 0.5rem;
}

.post-subtitle {
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 400;
    color: #6c757d;
    font-style: italic;
}

.post-meta {
    color: #6c757d;
    font-size: 0.875rem;
}

.post-meta span {
    margin-right: 1rem;
}

.post-content {
    margin-bottom: 1rem;
}

.post-actions {
    text-align: right;
}

.read-more {
    font-weight: 500;
}

/* Single Post */
.post-single {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.post-thumbnail-single {
    width: 100%;
    height: 300px;
    overflow: hidden;
    margin-top: 2rem;
}

.single-thumbnail-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.post-single .post-header {
    padding: 3rem 3rem 0 3rem;
}

.post-single .post-content {
    padding: 0 3rem;
}

.post-single .post-footer {
    padding: 0 3rem 3rem 3rem;
}

.breadcrumb {
    margin-bottom: 2rem;
    color: #6c757d;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: #007bff;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* Category Filter Info */
.category-filter-info {
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

.clear-filter {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.clear-filter:hover {
    text-decoration: underline;
}

/* Search */
.search-form {
    margin-bottom: 2rem;
}

.search-form form {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
}

.search-input {
    flex: 1;
    max-width: 400px;
}

.search-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.clear-search {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Filters */
.posts-filters {
    margin-bottom: 2rem;
}

.filter-tabs {
    display: flex;
    gap: 0;
    border-bottom: 1px solid #dee2e6;
}

.filter-tab {
    padding: 1rem 1.5rem;
    color: #6c757d;
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.filter-tab:hover,
.filter-tab.active {
    color: #007bff;
    border-bottom-color: #007bff;
    text-decoration: none;
}

/* Profile */
.profile-sections {
    display: grid;
    gap: 2rem;
}

.profile-section {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-grid {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.info-item label {
    font-weight: 500;
    margin-bottom: 0;
}

.security-tips ul {
    margin-left: 1.5rem;
    margin-top: 0.5rem;
}

.security-tips li {
    margin-bottom: 0.25rem;
}

/* Error Pages */
.error-page {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.error-page h1 {
    color: #dc3545;
    margin-bottom: 1rem;
}

.error-actions {
    margin-top: 2rem;
}

/* Footer */
.site-footer {
    background: #343a40;
    color: #adb5bd;
    text-align: center;
    padding: 2rem 0;
    margin-top: auto;
}

/* Bulk Delete Dropdown Styles */
.bulk-actions-dropdown {
    position: relative;
    display: inline-block;
}

.bulk-delete-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.bulk-delete-btn:hover {
    background: #c82333;
}

.bulk-dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    border-radius: 4px;
    z-index: 1000;
    border: 1px solid #dee2e6;
    margin-top: 4px;
}

.bulk-dropdown-content form {
    display: flex;
    flex-direction: column;
}

.bulk-dropdown-content button {
    background: none;
    border: none;
    padding: 12px 16px;
    text-align: left;
    cursor: pointer;
    transition: background 0.2s;
    border-bottom: 1px solid #f1f3f4;
}

.bulk-dropdown-content button:last-child {
    border-bottom: none;
    border-radius: 0 0 4px 4px;
}

.bulk-dropdown-content button:first-child {
    border-radius: 4px 4px 0 0;
}

.bulk-dropdown-content button:hover {
    background: #f8f9fa;
}

.bulk-dropdown-content button:active {
    background: #e9ecef;
}

/* Delete Button Styles */
.btn-delete {
    background: #dc3545 !important;
    color: white !important;
    border: 1px solid #dc3545 !important;
}

.btn-delete:hover {
    background: #c82333 !important;
    border-color: #bd2130 !important;
}

/* Article Action Buttons */
.article-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.article-actions .btn-sm {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .site-header .container,
    .admin-header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .main-nav ul,
    .admin-nav ul {
        flex-wrap: wrap;
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        flex-direction: column;
    }

    .search-form form {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-tabs {
        flex-wrap: wrap;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .post-single .post-header {
        padding: 2rem 1rem 0 1rem;
    }

    .post-single .post-content {
        padding: 0 1rem;
    }

    .post-single .post-footer {
        padding: 0 1rem 2rem 1rem;
    }

    .post-preview {
        flex-direction: column;
        min-height: auto;
    }

    .post-thumbnail {
        width: 100%;
        height: 150px;
        margin: 0;
    }

    .post-details {
        padding: 1.5rem;
    }

    .post-thumbnail-single {
        height: 200px;
    }

    .bulk-dropdown-content {
        right: 0;
        left: auto;
        min-width: 180px;
    }
    
    .news-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .news-header > div:first-child {
        text-align: center;
    }
}

/* Utility Classes */
.no-content {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.no-posts {
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

.content-header {
    margin-bottom: 2rem;
}

.post-count {
    color: #6c757d;
    margin-bottom: 0;
}

.post-excerpt {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.system-info {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
}

.pagination-container {
    margin-top: 2rem;
}

.posts-table {
    overflow-x: auto;
}

.post-form-container {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.post-form textarea {
    min-height: 300px;
    resize: vertical;
}

/* Reading Time and Sources Styles */
.post-reading-time {
    color: #6c757d;
    font-size: 0.875rem;
    margin-left: 0.5rem;
}

.post-reading-time::before {
    content: "📖";
    margin-right: 0.25rem;
}

.post-sources {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.post-sources h4 {
    margin-bottom: 1rem;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
}

.sources-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.sources-list li {
    margin-bottom: 0.5rem;
}

.source-link {
    color: #007bff;
    text-decoration: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: inline-block;
}

.source-link:hover {
    background-color: #007bff;
    color: white;
    text-decoration: none;
}

.source-link::before {
    content: "🔗";
    margin-right: 0.25rem;
}

/* Reading time in post listings */
.post-meta .post-reading-time {
    display: inline-block;
    background: #e9ecef;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Code Blocks and Inline Code Styling */
code {
    background: #2d3748;
    color: #e2e8f0;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 0.875rem;
}

pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1.5rem 0;
    border: 1px solid #4a5568;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

pre code {
    background: transparent;
    padding: 0;
    border-radius: 0;
    font-size: 0.875rem;
    line-height: 1.5;
    color: inherit;
}

/* Blockquote Styling */
blockquote {
    background: #f7fafc;
    border-left: 4px solid #4299e1;
    margin: 1.5rem 0;
    padding: 1rem 1.5rem;
    padding-left: 2rem;
    font-style: italic;
    color: #2d3748;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
}

blockquote::before {
    content: '"';
    font-size: 3rem;
    color: #4299e1;
    position: absolute;
    left: 0.5rem;
    top: -0.5rem;
    font-family: Georgia, serif;
    opacity: 0.3;
}

blockquote p {
    margin-bottom: 0;
}

blockquote p:last-child {
    margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .post-sources {
        padding: 1rem;
        margin-top: 1.5rem;
    }

    .post-meta .post-reading-time {
        margin-top: 0.5rem;
        margin-left: 0;
    }

    pre {
        padding: 1rem;
        margin: 1rem 0;
        font-size: 0.8rem;
    }

    blockquote {
        margin: 1rem 0;
        padding: 0.75rem 1rem;
        padding-left: 1.5rem;
    }

    blockquote::before {
        font-size: 2rem;
        left: 0.25rem;
        top: -0.25rem;
    }
}
