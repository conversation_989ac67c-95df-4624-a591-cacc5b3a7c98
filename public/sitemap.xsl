<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:sitemap="http://www.sitemaps.org/schemas/sitemap/0.9">
  <xsl:output method="html" version="1.0" encoding="UTF-8" indent="yes"/>
  
  <xsl:template match="/">
    <html lang="en">
      <head>
        <meta charset="UTF-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <title>XML Sitemap - Pixels CMS</title>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
          }
          
          .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
          }
          
          .header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
          }
          
          .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
          }
          
          .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
          }
          
          .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
          }
          
          .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px 40px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
          }
          
          .stat-card {
            background: white;
            padding: 5px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
          }
          
          .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
          }
          
          .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #3498db;
            margin-bottom: 8px;
          }
          
          .stat-label {
            color: #6c757d;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
          }
          
          .content {
            padding: 0;
          }
          
          .section {
            border-bottom: 1px solid #e9ecef;
          }
          
          .section:last-child {
            border-bottom: none;
          }
          
          .section-header {
            background: #f8f9fa;
            padding: 20px 40px;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            transition: background-color 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          
          .section-header:hover {
            background: #e9ecef;
          }
          
          .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
          }
          
          .section-count {
            background: #3498db;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
          }
          
          .toggle-icon {
            font-size: 1.2rem;
            color: #6c757d;
            transition: transform 0.3s ease;
          }
          
          .section.collapsed .toggle-icon {
            transform: rotate(180deg);
          }
          
          .url-list {
            display: none;
            padding: 0;
          }
          
          .section:not(.collapsed) .url-list {
            display: block;
          }
          
          .url-item {
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            gap: 20px;
            padding: 20px 40px;
            border-bottom: 1px solid #f8f9fa;
            transition: background-color 0.2s ease;
            align-items: center;
          }
          
          .url-item:hover {
            background: #f8f9fa;
          }
          
          .url-item:last-child {
            border-bottom: none;
          }
          
          .url-link {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
            word-break: break-all;
            transition: color 0.3s ease;
          }
          
          .url-link:hover {
            color: #2980b9;
            text-decoration: underline;
          }
          
          .url-meta {
            display: flex;
            gap: 15px;
            font-size: 0.9rem;
            color: #6c757d;
          }
          
          .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
          }
          
          .priority-bar {
            width: 60px;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
          }
          
          .priority-fill {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60);
            border-radius: 4px;
            transition: width 0.3s ease;
          }
          
          .changefreq {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: capitalize;
          }
          
          .freq-daily { background: #e8f5e8; color: #27ae60; }
          .freq-weekly { background: #fff3cd; color: #856404; }
          .freq-monthly { background: #d1ecf1; color: #0c5460; }
          
          .lastmod {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.85rem;
          }
          
          .footer {
            background: #2c3e50;
            color: white;
            padding: 30px 40px;
            text-align: center;
          }
          
          .footer p {
            margin-bottom: 10px;
            opacity: 0.8;
          }
          
          .footer a {
            color: #3498db;
            text-decoration: none;
          }
          
          .footer a:hover {
            text-decoration: underline;
          }
          
          @media (max-width: 768px) {
            .container {
              margin: 10px;
              border-radius: 12px;
            }
            
            .header {
              padding: 30px 20px;
            }
            
            .header h1 {
              font-size: 2rem;
            }
            
            .stats {
              grid-template-columns: 1fr;
              padding: 20px;
              gap: 15px;
            }
            
            .section-header,
            .url-item,
            .footer {
              padding-left: 20px;
              padding-right: 20px;
            }
            
            .url-item {
              grid-template-columns: 1fr;
              gap: 10px;
            }
            
            .url-meta {
              flex-wrap: wrap;
              gap: 10px;
            }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🗺️ XML Sitemap</h1>
            <p>This sitemap contains all publicly accessible pages and posts from this website.</p>
          </div>
          
          <div class="stats">
            <div class="stat-card">
              <div class="stat-number"><xsl:value-of select="count(//sitemap:url)"/></div>
              <div class="stat-label">Total URLs</div>
            </div>
            <div class="stat-card">
              <div class="stat-number"><xsl:value-of select="count(//sitemap:url[sitemap:priority='1.0'])"/></div>
              <div class="stat-label">High Priority</div>
            </div>
            <div class="stat-card">
              <div class="stat-number"><xsl:value-of select="count(//sitemap:url[sitemap:changefreq='daily'])"/></div>
              <div class="stat-label">Daily Updates</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">2025-06-21</div>
              <div class="stat-label">Generated Today</div>
            </div>
          </div>
          
          <div class="content">
            <!-- Homepage Section -->
            <div class="section">
              <div class="section-header" onclick="toggleSection(this)">
                <h2 class="section-title">🏠 Homepage</h2>
                <div style="display: flex; align-items: center; gap: 10px;">
                  <span class="section-count"><xsl:value-of select="count(//sitemap:url[sitemap:priority='1.0'])"/></span>
                  <span class="toggle-icon">▼</span>
                </div>
              </div>
              <div class="url-list">
                <xsl:for-each select="//sitemap:url[sitemap:priority='1.0']">
                  <div class="url-item">
                    <a href="{sitemap:loc}" class="url-link" target="_blank">
                      <xsl:value-of select="sitemap:loc"/>
                    </a>
                    <div class="url-meta">
                      <div class="meta-item">
                        <span class="lastmod"><xsl:value-of select="sitemap:lastmod"/></span>
                      </div>
                      <div class="meta-item">
                        <span class="changefreq freq-{sitemap:changefreq}"><xsl:value-of select="sitemap:changefreq"/></span>
                      </div>
                      <div class="meta-item">
                        <div class="priority-bar">
                          <div class="priority-fill" style="width: {sitemap:priority * 100}%"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </xsl:for-each>
              </div>
            </div>
            
            <!-- Categories Section -->
            <div class="section">
              <div class="section-header" onclick="toggleSection(this)">
                <h2 class="section-title">📂 Categories</h2>
                <div style="display: flex; align-items: center; gap: 10px;">
                  <span class="section-count"><xsl:value-of select="count(//sitemap:url[sitemap:priority='0.8'])"/></span>
                  <span class="toggle-icon">▼</span>
                </div>
              </div>
              <div class="url-list">
                <xsl:for-each select="//sitemap:url[sitemap:priority='0.8']">
                  <div class="url-item">
                    <a href="{sitemap:loc}" class="url-link" target="_blank">
                      <xsl:value-of select="sitemap:loc"/>
                    </a>
                    <div class="url-meta">
                      <div class="meta-item">
                        <span class="lastmod"><xsl:value-of select="sitemap:lastmod"/></span>
                      </div>
                      <div class="meta-item">
                        <span class="changefreq freq-{sitemap:changefreq}"><xsl:value-of select="sitemap:changefreq"/></span>
                      </div>
                      <div class="meta-item">
                        <div class="priority-bar">
                          <div class="priority-fill" style="width: {sitemap:priority * 100}%"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </xsl:for-each>
              </div>
            </div>
            
            <!-- Posts Section -->
            <div class="section">
              <div class="section-header" onclick="toggleSection(this)">
                <h2 class="section-title">📄 Posts</h2>
                <div style="display: flex; align-items: center; gap: 10px;">
                  <span class="section-count"><xsl:value-of select="count(//sitemap:url[sitemap:priority='0.6'])"/></span>
                  <span class="toggle-icon">▼</span>
                </div>
              </div>
              <div class="url-list">
                <xsl:for-each select="//sitemap:url[sitemap:priority='0.6']">
                  <xsl:sort select="sitemap:lastmod" order="descending"/>
                  <div class="url-item">
                    <a href="{sitemap:loc}" class="url-link" target="_blank">
                      <xsl:value-of select="sitemap:loc"/>
                    </a>
                    <div class="url-meta">
                      <div class="meta-item">
                        <span class="lastmod"><xsl:value-of select="sitemap:lastmod"/></span>
                      </div>
                      <div class="meta-item">
                        <span class="changefreq freq-{sitemap:changefreq}"><xsl:value-of select="sitemap:changefreq"/></span>
                      </div>
                      <div class="meta-item">
                        <div class="priority-bar">
                          <div class="priority-fill" style="width: {sitemap:priority * 100}%"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </xsl:for-each>
              </div>
            </div>
          </div>
          
          <div class="footer">
            <p>This XML sitemap is designed for search engines and can be viewed by humans.</p>
            <p>Generated by <a href="https://github.com/J4GL/pixels" target="_blank">Pixels CMS</a> • 
            Total URLs: <xsl:value-of select="count(//sitemap:url)"/> • 
            Last Updated: 2025-06-21</p>
          </div>
        </div>
        
        <script>
          function toggleSection(header) {
            const section = header.parentElement;
            section.classList.toggle('collapsed');
          }
          
          // Initially collapse sections with many items for better performance
          document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.section');
            sections.forEach(function(section, index) {
              if (index > 0) { // Keep homepage expanded
                section.classList.add('collapsed');
              }
            });
          });
        </script>
      </body>
    </html>
  </xsl:template>
</xsl:stylesheet>
