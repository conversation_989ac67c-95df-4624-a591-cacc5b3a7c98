<?php
/**
 * Dynamic XML Sitemap Generator
 * Generates a complete sitemap including homepage, categories, and all published posts
 */

// Set proper XML content type headers
header('Content-Type: application/xml; charset=UTF-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT'); // Cache for 1 hour

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once __DIR__ . "/../src/includes/init.php";
}

/**
 * Generate sitemap XML content
 */
function generateSitemap() {
    error_log("generateSitemap start");
    global $postManager;
    
    try {
        // Get base URL (remove script path for root domain)
        $baseUrl = getBaseUrl();
        // Remove /public from the end if present
        $baseUrl = rtrim($baseUrl, '/');
        if (str_ends_with($baseUrl, '/public')) {
            $baseUrl = substr($baseUrl, 0, -7);
        }
        
        error_log("generateSitemap baseUrl: " . $baseUrl);
        
        // Start XML output
        $xml = generateXmlHeader();
        
        // Add homepage
        $xml .= generateHomepageUrl($baseUrl);
        
        // Add category pages
        $xml .= generateCategoryUrls($baseUrl);
        
        // Add all published posts
        $xml .= generatePostUrls($baseUrl);
        
        // Close XML
        $xml .= "</urlset>";
        
        error_log("generateSitemap end - XML length: " . strlen($xml));
        return $xml;
        
    } catch (Exception $e) {
        error_log("Sitemap generation error: " . $e->getMessage());
        error_log("generateSitemap error: " . $e->getMessage());
        return generateErrorSitemap();
    }
}

/**
 * Generate XML header with proper namespace and modern styling
 */
function generateXmlHeader() {
    $header = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $header .= '<?xml-stylesheet type="text/xsl" href="sitemap.xsl"?>' . "\n";
    $header .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    return $header;
}

/**
 * Generate homepage URL entry
 */
function generateHomepageUrl($baseUrl) {
    $today = date('Y-m-d');
    
    $xml = "  <url>\n";
    $xml .= "    <loc>" . Security::escape($baseUrl) . "/</loc>\n";
    $xml .= "    <lastmod>" . $today . "</lastmod>\n";
    $xml .= "    <changefreq>daily</changefreq>\n";
    $xml .= "    <priority>1.0</priority>\n";
    $xml .= "  </url>\n";
    
    return $xml;
}

/**
 * Generate category page URLs
 */
function generateCategoryUrls($baseUrl) {
    $xml = "";
    $categories = Post::getValidCategories();
    $today = date('Y-m-d');
    
    foreach ($categories as $category) {
        $xml .= "  <url>\n";
        $xml .= "    <loc>" . Security::escape($baseUrl) . "/index.php?category=" . urlencode($category) . "</loc>\n";
        $xml .= "    <lastmod>" . $today . "</lastmod>\n";
        $xml .= "    <changefreq>weekly</changefreq>\n";
        $xml .= "    <priority>0.8</priority>\n";
        $xml .= "  </url>\n";
    }
    
    return $xml;
}

/**
 * Generate URLs for all published posts
 */
function generatePostUrls($baseUrl) {
    global $postManager;
    $xml = "";
    
    try {
        // Get all published posts (no pagination limit for sitemap)
        $result = $postManager->getAllPosts(1, 1000, true); // Published only
        $posts = $result['posts'] ?? [];
        
        foreach ($posts as $post) {
            $xml .= generateSinglePostUrl($baseUrl, $post);
        }
        
        return $xml;
        
    } catch (Exception $e) {
        error_log("Error generating post URLs: " . $e->getMessage());
        return "";
    }
}

/**
 * Generate URL entry for a single post
 */
function generateSinglePostUrl($baseUrl, $post) {
    try {
        // Generate SEO-friendly URL
        $postUrl = generatePostUrl($post);
        $fullUrl = $baseUrl . $postUrl;
        
        // Use post's updated date for lastmod
        $lastmod = date('Y-m-d', strtotime($post['updated_at']));
        
        $xml = "  <url>\n";
        $xml .= "    <loc>" . Security::escape($fullUrl) . "</loc>\n";
        $xml .= "    <lastmod>" . $lastmod . "</lastmod>\n";
        $xml .= "    <changefreq>monthly</changefreq>\n";
        $xml .= "    <priority>0.6</priority>\n";
        $xml .= "  </url>\n";
        
        return $xml;
        
    } catch (Exception $e) {
        error_log("Error generating URL for post " . ($post['id'] ?? 'unknown') . ": " . $e->getMessage());
        return "";
    }
}

/**
 * Generate minimal error sitemap if something fails
 */
function generateErrorSitemap() {
    $baseUrl = getBaseUrl();
    $today = date('Y-m-d');
    
    $xml = generateXmlHeader();
    $xml .= "  <url>\n";
    $xml .= "    <loc>" . Security::escape($baseUrl) . "/</loc>\n";
    $xml .= "    <lastmod>" . $today . "</lastmod>\n";
    $xml .= "    <changefreq>daily</changefreq>\n";
    $xml .= "    <priority>1.0</priority>\n";
    $xml .= "  </url>\n";
    $xml .= "</urlset>";
    
    return $xml;
}

// Generate and output the sitemap
echo generateSitemap();
?>
