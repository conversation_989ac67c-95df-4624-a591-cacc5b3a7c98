<?php
/**
 * Router for PHP Development Server
 * Handles static files and routes dynamic requests
 *
 * Note: This file is only needed when using PHP's built-in development server.
 * For production, use app.php as the main entry point.
 */

// Get the requested URI
$uri = $_SERVER['REQUEST_URI'];
$path = parse_url($uri, PHP_URL_PATH);



// Check if it's a file that exists
$filePath = __DIR__ . $path;
if (file_exists($filePath) && is_file($filePath)) {
    $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));

    // Let PHP development server handle static files directly
    $staticExtensions = ['css', 'js', 'png', 'jpg', 'jpeg', 'gif', 'ico', 'svg', 'woff', 'woff2', 'ttf', 'eot', 'xsl'];
    if (in_array($extension, $staticExtensions)) {
        return false;
    }

    // Let PHP development server handle PHP files directly (except app.php)
    if ($extension === 'php' && basename($path) !== 'app.php') {
        return false;
    }
}

// For routes that don't match existing files, use the server-agnostic router
require_once 'app.php';
?>
