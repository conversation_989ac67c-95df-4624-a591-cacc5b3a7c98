#!/bin/bash

# Installation script for news scraper dependencies

echo "🚀 Installing Python dependencies for news scraper..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 first."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3 first."
    exit 1
fi

# Install required packages
echo "📦 Installing undetected-chromedriver..."
pip3 install undetected-chromedriver

echo "📦 Installing selenium..."
pip3 install selenium

echo "📦 Installing pyyaml..."
pip3 install pyyaml

echo "📦 Installing requests..."
pip3 install requests

echo "📦 Installing html2text..."
pip3 install html2text

# Create logs directory if it doesn't exist
if [ ! -d "logs" ]; then
    echo "📁 Creating logs directory..."
    mkdir -p logs
fi

# Make scraper.py executable
chmod +x scraper.py

# Run database migration
echo "🗄️ Creating news_articles table..."
php migrate-news-table.php

echo "✅ Installation completed successfully!"
echo ""
echo "🔧 Usage:"
echo "  - Run scraper manually: python3 scraper.py"
echo "  - View news in admin panel: /admin/news.php"
echo ""
echo "📋 Next steps:"
echo "  1. Add more YAML configuration files in public/admin/news_src/"
echo "  2. Set up a cron job to run scraper.py automatically"
echo "  3. Configure your news sources in the YAML files"
