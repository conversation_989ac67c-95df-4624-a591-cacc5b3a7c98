# Remove Direct Post.php - Use Only SEO-Friendly URLs

## Overview
Remove the direct `post.php` file and ensure all post views use the SEO-friendly URL format (`/category/slug/`). This will simplify the codebase and enforce a consistent URL structure.

## Architecture Changes

```mermaid
flowchart TD
    A[User Request] --> B{URL Format}
    B -->|/category/slug/| C[post-seo.php]
    B -->|/post.php?id=X| D[Redirect to SEO URL]
    B -->|Direct post.php access| E[404 Not Found]
    
    C --> F[Display Post Content]
    D --> C
    E --> G[Error Page]
    
    H[Admin Links] --> I[generatePostUrl Function]
    I --> J[SEO-Friendly URLs Only]
```

## Database Schema
No database changes required - existing posts table remains unchanged.

## Implementation Checklist

### Phase 1: Update Router and Redirects
- [x] Remove backward compatibility route for `/post.php` from Router
- [x] Add redirect logic for old `/post.php?id=X` URLs to SEO URLs
- [x] Test that direct post.php access returns 404

### Phase 2: Update Tests
- [x] Update Playwright tests to use SEO URLs instead of post.php?id=X
- [x] Add test for redirect from old URL format to new format
- [x] Ensure all tests pass with SEO URLs only

### Phase 3: Remove Files and References
- [x] Delete the `/public/post.php` file
- [x] Update any remaining documentation references
- [x] Clean up any backup files or references

### Phase 4: Verification
- [x] Run full test suite to ensure no broken functionality
- [x] Verify all post links in admin use SEO URLs
- [x] Confirm old URLs properly redirect to new format

## Files to Modify

- **Router**: `src/classes/Router.php` - Remove backward compatibility route
- **Tests**: `tests/subtitle-feature.spec.js` - Update to use SEO URLs
- **Documentation**: Update any references in PLAN files
- **Remove**: `public/post.php` - Delete the file entirely

## Risk Assessment

- **Low Risk**: All functionality already exists in post-seo.php
- **SEO Impact**: Positive - enforces consistent URL structure
- **User Impact**: None - admin links already use SEO URLs
- **Testing**: Required to ensure no broken links

## Success Criteria

1. `/post.php?id=X` URLs redirect to `/category/slug/` format
2. Direct access to `post.php` returns 404
3. All tests pass using SEO URLs
4. Admin post links continue to work
5. No broken functionality detected
